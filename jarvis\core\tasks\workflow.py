"""
Workflow Automation for JARVIS AI Assistant
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
import uuid

from .base import Task, TaskResult, TaskStatus
from .executor import TaskExecutor

logger = logging.getLogger(__name__)


@dataclass
class WorkflowStep:
    """A single step in a workflow"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    task: Task = None
    depends_on: List[str] = field(default_factory=list)
    condition: Optional[Callable[[Dict[str, Any]], bool]] = None
    retry_on_failure: bool = False
    max_retries: int = 3
    timeout: Optional[float] = None


@dataclass
class Workflow:
    """A workflow containing multiple steps"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    steps: List[WorkflowStep] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    status: str = "pending"  # pending, running, completed, failed, cancelled
    results: Dict[str, TaskResult] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)


class WorkflowEngine:
    """Engine for executing workflows"""
    
    def __init__(self, task_executor: TaskExecutor):
        """Initialize workflow engine"""
        self.task_executor = task_executor
        self.active_workflows: Dict[str, Workflow] = {}
        self.completed_workflows: List[Workflow] = []
        
        logger.info("Workflow engine initialized")
    
    async def execute_workflow(self, workflow: Workflow) -> Workflow:
        """
        Execute a workflow
        
        Args:
            workflow: Workflow to execute
            
        Returns:
            Completed workflow with results
        """
        logger.info(f"Starting workflow: {workflow.name}")
        workflow.status = "running"
        self.active_workflows[workflow.id] = workflow
        
        try:
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(workflow)
            
            # Execute steps in dependency order
            executed_steps = set()
            
            while len(executed_steps) < len(workflow.steps):
                # Find steps that can be executed (dependencies satisfied)
                ready_steps = []
                for step in workflow.steps:
                    if (step.id not in executed_steps and 
                        all(dep in executed_steps for dep in step.depends_on)):
                        
                        # Check condition if present
                        if step.condition is None or step.condition(workflow.context):
                            ready_steps.append(step)
                
                if not ready_steps:
                    # Check if we're stuck due to failed dependencies
                    remaining_steps = [s for s in workflow.steps if s.id not in executed_steps]
                    if remaining_steps:
                        workflow.status = "failed"
                        logger.error(f"Workflow {workflow.name} stuck - no executable steps remaining")
                        break
                    else:
                        break
                
                # Execute ready steps (can be done in parallel)
                step_tasks = []
                for step in ready_steps:
                    step_tasks.append(self._execute_step(step, workflow))
                
                # Wait for all steps to complete
                step_results = await asyncio.gather(*step_tasks, return_exceptions=True)
                
                # Process results
                for i, result in enumerate(step_results):
                    step = ready_steps[i]
                    if isinstance(result, Exception):
                        logger.error(f"Step {step.name} failed: {result}")
                        workflow.results[step.id] = TaskResult(
                            success=False,
                            error=str(result)
                        )
                        
                        if not step.retry_on_failure:
                            workflow.status = "failed"
                            break
                    else:
                        workflow.results[step.id] = result
                        executed_steps.add(step.id)
                        
                        # Update workflow context with step results
                        if result.success and result.data:
                            workflow.context[f"step_{step.name}_result"] = result.data
                
                # Break if workflow failed
                if workflow.status == "failed":
                    break
            
            # Mark workflow as completed if all steps executed successfully
            if workflow.status == "running":
                workflow.status = "completed"
                logger.info(f"Workflow {workflow.name} completed successfully")
            
        except Exception as e:
            logger.error(f"Workflow execution error: {e}")
            workflow.status = "failed"
            workflow.context["error"] = str(e)
        
        finally:
            # Move to completed workflows
            if workflow.id in self.active_workflows:
                del self.active_workflows[workflow.id]
            self.completed_workflows.append(workflow)
        
        return workflow
    
    async def _execute_step(self, step: WorkflowStep, workflow: Workflow) -> TaskResult:
        """Execute a single workflow step"""
        logger.debug(f"Executing step: {step.name}")
        
        try:
            # Set timeout if specified
            if step.timeout:
                step.task.timeout = step.timeout
            
            # Execute the task
            result = await self.task_executor.execute_task(step.task)
            
            # Handle retry logic
            if not result.success and step.retry_on_failure:
                for retry in range(step.max_retries):
                    logger.info(f"Retrying step {step.name} (attempt {retry + 1})")
                    await asyncio.sleep(1)  # Brief delay between retries
                    result = await self.task_executor.execute_task(step.task)
                    if result.success:
                        break
            
            return result
            
        except Exception as e:
            logger.error(f"Step execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    def _build_dependency_graph(self, workflow: Workflow) -> Dict[str, List[str]]:
        """Build dependency graph for workflow steps"""
        graph = {}
        for step in workflow.steps:
            graph[step.id] = step.depends_on.copy()
        return graph
    
    def create_nlp_workflow(self, nlp_result, original_text: str) -> Optional[Workflow]:
        """
        Create a workflow from NLP processing result
        
        Args:
            nlp_result: Result from NLP processing
            original_text: Original user input
            
        Returns:
            Workflow or None if no workflow needed
        """
        if not nlp_result.command:
            return None
        
        # Create main task from NLP result
        main_task = Task(
            name=f"Execute {nlp_result.intent}",
            description=f"Execute command: {nlp_result.command}",
            command=nlp_result.command,
            parameters=nlp_result.parameters or {}
        )
        
        # Create workflow
        workflow = Workflow(
            name=f"NLP Workflow: {nlp_result.intent}",
            description=f"Workflow generated from user input: {original_text[:50]}...",
            context={
                "original_text": original_text,
                "intent": nlp_result.intent,
                "confidence": nlp_result.confidence,
                "entities": nlp_result.entities
            }
        )
        
        # Create main step
        main_step = WorkflowStep(
            name="main_execution",
            task=main_task,
            retry_on_failure=False,
            timeout=30.0
        )
        
        workflow.steps.append(main_step)
        
        # Add pre/post processing steps based on intent
        self._add_intent_specific_steps(workflow, nlp_result)
        
        return workflow
    
    def _add_intent_specific_steps(self, workflow: Workflow, nlp_result):
        """Add intent-specific pre/post processing steps"""
        intent = nlp_result.intent
        
        if intent == "calculation":
            # Add validation step for math operations
            validation_task = Task(
                name="Validate calculation",
                command="system.validate_math",
                parameters={"expression": nlp_result.parameters.get("expression", "")}
            )
            
            validation_step = WorkflowStep(
                name="validation",
                task=validation_task
            )
            
            # Make main step depend on validation
            workflow.steps[0].depends_on.append(validation_step.id)
            workflow.steps.insert(0, validation_step)
        
        elif intent == "file_operation":
            # Add safety check for file operations
            safety_task = Task(
                name="Safety check",
                command="system.safety_check",
                parameters={"operation": nlp_result.command}
            )
            
            safety_step = WorkflowStep(
                name="safety_check",
                task=safety_task,
                condition=lambda ctx: ctx.get("safe_mode", True)
            )
            
            workflow.steps[0].depends_on.append(safety_step.id)
            workflow.steps.insert(0, safety_step)
        
        elif intent == "search":
            # Add result formatting step
            format_task = Task(
                name="Format search results",
                command="system.format_results",
                parameters={"result_type": "search"}
            )
            
            format_step = WorkflowStep(
                name="format_results",
                task=format_task,
                depends_on=[workflow.steps[0].id]
            )
            
            workflow.steps.append(format_step)
    
    def get_workflow_status(self, workflow_id: str) -> Optional[str]:
        """Get status of a workflow"""
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id].status
        
        for workflow in self.completed_workflows:
            if workflow.id == workflow_id:
                return workflow.status
        
        return None
    
    def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel an active workflow"""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            workflow.status = "cancelled"
            logger.info(f"Cancelled workflow: {workflow.name}")
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get workflow engine statistics"""
        total_workflows = len(self.completed_workflows) + len(self.active_workflows)
        completed_count = len([w for w in self.completed_workflows if w.status == "completed"])
        failed_count = len([w for w in self.completed_workflows if w.status == "failed"])
        
        return {
            "total_workflows": total_workflows,
            "active_workflows": len(self.active_workflows),
            "completed_workflows": completed_count,
            "failed_workflows": failed_count,
            "success_rate": completed_count / total_workflows if total_workflows > 0 else 0.0
        }
