"""
Main NLP Processor for JARVIS AI Assistant
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .intent_recognition import IntentRecognizer
from .entity_extraction import EntityExtractor
from .command_parser import CommandParser

logger = logging.getLogger(__name__)


@dataclass
class NLPResult:
    """Result of NLP processing"""
    original_text: str
    intent: str
    confidence: float
    entities: Dict[str, Any]
    command: Optional[str] = None
    parameters: Dict[str, Any] = None
    requires_confirmation: bool = False
    suggested_actions: List[str] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.suggested_actions is None:
            self.suggested_actions = []


class NLPProcessor:
    """Main NLP processor that coordinates all NLP tasks"""
    
    def __init__(self):
        """Initialize the NLP processor"""
        self.intent_recognizer = IntentRecognizer()
        self.entity_extractor = EntityExtractor()
        self.command_parser = CommandParser()
        
        # Preprocessing patterns
        self.preprocessing_patterns = [
            (r'\s+', ' '),  # Multiple spaces to single space
            (r'[^\w\s\.\?\!\,\-\+\*\/\=\(\)]', ''),  # Remove special chars except basic punctuation
        ]
        
        logger.info("NLP Processor initialized")
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess input text
        
        Args:
            text: Raw input text
            
        Returns:
            Preprocessed text
        """
        # Convert to lowercase and strip
        processed = text.lower().strip()
        
        # Apply preprocessing patterns
        for pattern, replacement in self.preprocessing_patterns:
            processed = re.sub(pattern, replacement, processed)
        
        return processed.strip()
    
    def process(self, text: str, context: Optional[Dict[str, Any]] = None) -> NLPResult:
        """
        Process natural language input
        
        Args:
            text: Input text to process
            context: Optional context information
            
        Returns:
            NLP processing result
        """
        try:
            # Preprocess text
            processed_text = self.preprocess_text(text)
            
            # Recognize intent
            intent, confidence = self.intent_recognizer.recognize(processed_text, context)
            
            # Extract entities
            entities = self.entity_extractor.extract(processed_text, intent)
            
            # Parse command if applicable
            command, parameters = self.command_parser.parse(processed_text, intent, entities)
            
            # Determine if confirmation is required
            requires_confirmation = self._requires_confirmation(intent, command, entities)
            
            # Generate suggested actions
            suggested_actions = self._generate_suggested_actions(intent, entities, context)
            
            result = NLPResult(
                original_text=text,
                intent=intent,
                confidence=confidence,
                entities=entities,
                command=command,
                parameters=parameters,
                requires_confirmation=requires_confirmation,
                suggested_actions=suggested_actions
            )
            
            logger.debug(f"NLP processing complete: intent={intent}, confidence={confidence}")
            return result
            
        except Exception as e:
            logger.error(f"NLP processing error: {e}")
            # Return fallback result
            return NLPResult(
                original_text=text,
                intent="unknown",
                confidence=0.0,
                entities={},
                command=None,
                parameters={}
            )
    
    def _requires_confirmation(self, intent: str, command: Optional[str], entities: Dict[str, Any]) -> bool:
        """
        Determine if the action requires user confirmation
        
        Args:
            intent: Recognized intent
            command: Parsed command
            entities: Extracted entities
            
        Returns:
            True if confirmation is required
        """
        # High-risk intents that always require confirmation
        high_risk_intents = [
            "delete_file",
            "system_shutdown",
            "install_software",
            "modify_system_settings",
            "send_email",
            "make_purchase"
        ]
        
        if intent in high_risk_intents:
            return True
        
        # Check for destructive operations in entities
        if entities.get("operation_type") in ["delete", "remove", "uninstall", "format"]:
            return True
        
        # Check for system-level commands
        if command and command.startswith("system."):
            return True
        
        return False
    
    def _generate_suggested_actions(
        self, 
        intent: str, 
        entities: Dict[str, Any], 
        context: Optional[Dict[str, Any]]
    ) -> List[str]:
        """
        Generate suggested follow-up actions
        
        Args:
            intent: Recognized intent
            entities: Extracted entities
            context: Context information
            
        Returns:
            List of suggested actions
        """
        suggestions = []
        
        # Intent-based suggestions
        if intent == "get_time":
            suggestions.extend([
                "Set a reminder",
                "Check calendar",
                "Get weather forecast"
            ])
        elif intent == "get_weather":
            suggestions.extend([
                "Get extended forecast",
                "Check weather alerts",
                "Plan outdoor activities"
            ])
        elif intent == "file_operation":
            suggestions.extend([
                "List directory contents",
                "Check file properties",
                "Create backup"
            ])
        elif intent == "calculation":
            suggestions.extend([
                "Save result to memory",
                "Perform related calculations",
                "Convert units"
            ])
        elif intent == "search":
            suggestions.extend([
                "Refine search",
                "Save search results",
                "Search related topics"
            ])
        
        # Entity-based suggestions
        if "person" in entities:
            suggestions.append(f"Get contact info for {entities['person']}")
        
        if "location" in entities:
            suggestions.append(f"Get directions to {entities['location']}")
        
        if "date" in entities or "time" in entities:
            suggestions.append("Add to calendar")
        
        # Context-based suggestions
        if context:
            if context.get("previous_intent") == "search":
                suggestions.append("Search for more information")
            
            if context.get("conversation_length", 0) > 5:
                suggestions.append("Summarize conversation")
        
        # Remove duplicates and limit to top 3
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:3]
    
    def extract_keywords(self, text: str) -> List[str]:
        """
        Extract keywords from text
        
        Args:
            text: Input text
            
        Returns:
            List of keywords
        """
        # Simple keyword extraction (can be enhanced with more sophisticated methods)
        processed = self.preprocess_text(text)
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'can', 'may', 'might', 'must', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her',
            'its', 'our', 'their', 'this', 'that', 'these', 'those'
        }
        
        words = processed.split()
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get NLP processing statistics"""
        return {
            "intent_recognizer": self.intent_recognizer.get_stats(),
            "entity_extractor": self.entity_extractor.get_stats(),
            "command_parser": self.command_parser.get_stats()
        }
