"""
Audit Logging System for JARVIS AI Assistant
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum
import threading
import queue
import time

from ...config import config

logger = logging.getLogger(__name__)


class AuditEventType(Enum):
    """Types of audit events"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA_ACCESS = "data_access"
    SYSTEM_CHANGE = "system_change"
    SECURITY_EVENT = "security_event"
    USER_ACTION = "user_action"
    API_CALL = "api_call"
    ERROR = "error"


class AuditSeverity(Enum):
    """Audit event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Audit event representation"""
    event_id: str
    event_type: AuditEventType
    severity: AuditSeverity
    timestamp: datetime
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None
    action: str = ""
    resource: Optional[str] = None
    result: str = ""  # success, failure, error
    details: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AuditLogger:
    """Comprehensive audit logging system"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """Initialize audit logger"""
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        self.audit_dir = self.data_dir / "audit"
        
        # Ensure directories exist
        self.data_dir.mkdir(exist_ok=True)
        self.audit_dir.mkdir(exist_ok=True)
        
        # Configuration
        self.max_log_size = config.get("audit.max_log_size_mb", 100) * 1024 * 1024  # Convert to bytes
        self.retention_days = config.get("audit.retention_days", 90)
        self.batch_size = config.get("audit.batch_size", 100)
        self.flush_interval = config.get("audit.flush_interval_seconds", 30)
        
        # Current log file
        self.current_log_file = self.audit_dir / f"audit_{datetime.now().strftime('%Y%m%d')}.jsonl"
        
        # Event queue for async logging
        self.event_queue = queue.Queue()
        self.is_running = True
        
        # Start background logging thread
        self.logging_thread = threading.Thread(target=self._background_logger, daemon=True)
        self.logging_thread.start()
        
        # Event counters
        self.event_counters = {event_type: 0 for event_type in AuditEventType}
        self.severity_counters = {severity: 0 for severity in AuditSeverity}
        
        logger.info("Audit logger initialized")
    
    def log_event(
        self,
        event_type: AuditEventType,
        action: str,
        severity: AuditSeverity = AuditSeverity.LOW,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        resource: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log an audit event"""
        try:
            import uuid
            
            event = AuditEvent(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                severity=severity,
                timestamp=datetime.now(),
                user_id=user_id,
                session_id=session_id,
                source_ip=source_ip,
                user_agent=user_agent,
                action=action,
                resource=resource,
                result=result,
                details=details or {},
                metadata=metadata or {}
            )
            
            # Add to queue for async processing
            self.event_queue.put(event)
            
            # Update counters
            self.event_counters[event_type] += 1
            self.severity_counters[severity] += 1
            
            # Log critical events immediately
            if severity == AuditSeverity.CRITICAL:
                logger.critical(f"CRITICAL AUDIT EVENT: {action} - {details}")
            
        except Exception as e:
            logger.error(f"Error logging audit event: {e}")
    
    def log_authentication_event(
        self,
        action: str,
        user_id: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None
    ):
        """Log authentication event"""
        severity = AuditSeverity.MEDIUM if result == "failure" else AuditSeverity.LOW
        
        self.log_event(
            event_type=AuditEventType.AUTHENTICATION,
            action=action,
            severity=severity,
            user_id=user_id,
            result=result,
            details=details,
            source_ip=source_ip
        )
    
    def log_authorization_event(
        self,
        action: str,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None
    ):
        """Log authorization event"""
        severity = AuditSeverity.HIGH if result == "failure" else AuditSeverity.LOW
        
        self.log_event(
            event_type=AuditEventType.AUTHORIZATION,
            action=action,
            severity=severity,
            user_id=user_id,
            resource=resource,
            result=result,
            details=details
        )
    
    def log_data_access_event(
        self,
        action: str,
        resource: str,
        user_id: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None
    ):
        """Log data access event"""
        self.log_event(
            event_type=AuditEventType.DATA_ACCESS,
            action=action,
            severity=AuditSeverity.LOW,
            user_id=user_id,
            resource=resource,
            result=result,
            details=details
        )
    
    def log_security_event(
        self,
        action: str,
        severity: AuditSeverity = AuditSeverity.HIGH,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None
    ):
        """Log security event"""
        self.log_event(
            event_type=AuditEventType.SECURITY_EVENT,
            action=action,
            severity=severity,
            user_id=user_id,
            details=details,
            source_ip=source_ip
        )
    
    def log_system_change_event(
        self,
        action: str,
        resource: str,
        user_id: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None
    ):
        """Log system change event"""
        self.log_event(
            event_type=AuditEventType.SYSTEM_CHANGE,
            action=action,
            severity=AuditSeverity.MEDIUM,
            user_id=user_id,
            resource=resource,
            result=result,
            details=details
        )
    
    def log_user_action_event(
        self,
        action: str,
        user_id: str,
        resource: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ):
        """Log user action event"""
        self.log_event(
            event_type=AuditEventType.USER_ACTION,
            action=action,
            severity=AuditSeverity.LOW,
            user_id=user_id,
            session_id=session_id,
            resource=resource,
            result=result,
            details=details
        )
    
    def log_api_call_event(
        self,
        action: str,
        user_id: Optional[str] = None,
        result: str = "success",
        details: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log API call event"""
        self.log_event(
            event_type=AuditEventType.API_CALL,
            action=action,
            severity=AuditSeverity.LOW,
            user_id=user_id,
            result=result,
            details=details,
            source_ip=source_ip,
            user_agent=user_agent
        )
    
    def log_error_event(
        self,
        action: str,
        error_message: str,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.MEDIUM
    ):
        """Log error event"""
        error_details = details or {}
        error_details["error_message"] = error_message
        
        self.log_event(
            event_type=AuditEventType.ERROR,
            action=action,
            severity=severity,
            user_id=user_id,
            result="error",
            details=error_details
        )
    
    def search_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        event_type: Optional[AuditEventType] = None,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        severity: Optional[AuditSeverity] = None,
        limit: int = 1000
    ) -> List[AuditEvent]:
        """Search audit events"""
        try:
            events = []
            
            # Determine date range
            if not start_date:
                start_date = datetime.now() - timedelta(days=7)  # Default to last 7 days
            if not end_date:
                end_date = datetime.now()
            
            # Get log files in date range
            log_files = self._get_log_files_in_range(start_date, end_date)
            
            for log_file in log_files:
                if not log_file.exists():
                    continue
                
                with open(log_file, 'r') as f:
                    for line in f:
                        try:
                            event_data = json.loads(line.strip())
                            
                            # Convert to AuditEvent
                            event_data['event_type'] = AuditEventType(event_data['event_type'])
                            event_data['severity'] = AuditSeverity(event_data['severity'])
                            event_data['timestamp'] = datetime.fromisoformat(event_data['timestamp'])
                            
                            event = AuditEvent(**event_data)
                            
                            # Apply filters
                            if event_type and event.event_type != event_type:
                                continue
                            if user_id and event.user_id != user_id:
                                continue
                            if action and action.lower() not in event.action.lower():
                                continue
                            if severity and event.severity != severity:
                                continue
                            if event.timestamp < start_date or event.timestamp > end_date:
                                continue
                            
                            events.append(event)
                            
                            if len(events) >= limit:
                                break
                                
                        except (json.JSONDecodeError, KeyError, ValueError) as e:
                            logger.warning(f"Error parsing audit event: {e}")
                            continue
                
                if len(events) >= limit:
                    break
            
            # Sort by timestamp (newest first)
            events.sort(key=lambda e: e.timestamp, reverse=True)
            
            return events[:limit]
            
        except Exception as e:
            logger.error(f"Error searching audit events: {e}")
            return []
    
    def get_audit_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get audit statistics"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Get all events in date range
            events = self.search_events(start_date=start_date, end_date=end_date, limit=10000)
            
            # Calculate statistics
            stats = {
                "total_events": len(events),
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": days
                },
                "events_by_type": {},
                "events_by_severity": {},
                "events_by_result": {},
                "top_users": {},
                "top_actions": {},
                "events_by_day": {}
            }
            
            # Count by type
            for event_type in AuditEventType:
                count = sum(1 for e in events if e.event_type == event_type)
                stats["events_by_type"][event_type.value] = count
            
            # Count by severity
            for severity in AuditSeverity:
                count = sum(1 for e in events if e.severity == severity)
                stats["events_by_severity"][severity.value] = count
            
            # Count by result
            results = {}
            for event in events:
                results[event.result] = results.get(event.result, 0) + 1
            stats["events_by_result"] = results
            
            # Top users
            users = {}
            for event in events:
                if event.user_id:
                    users[event.user_id] = users.get(event.user_id, 0) + 1
            stats["top_users"] = dict(sorted(users.items(), key=lambda x: x[1], reverse=True)[:10])
            
            # Top actions
            actions = {}
            for event in events:
                actions[event.action] = actions.get(event.action, 0) + 1
            stats["top_actions"] = dict(sorted(actions.items(), key=lambda x: x[1], reverse=True)[:10])
            
            # Events by day
            for event in events:
                day = event.timestamp.date().isoformat()
                stats["events_by_day"][day] = stats["events_by_day"].get(day, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting audit statistics: {e}")
            return {}
    
    def cleanup_old_logs(self):
        """Clean up old audit logs"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            
            deleted_count = 0
            for log_file in self.audit_dir.glob("audit_*.jsonl"):
                try:
                    # Extract date from filename
                    date_str = log_file.stem.split('_')[1]
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    
                    if file_date.date() < cutoff_date.date():
                        log_file.unlink()
                        deleted_count += 1
                        
                except (ValueError, IndexError):
                    # Skip files with invalid names
                    continue
            
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} old audit log files")
                
        except Exception as e:
            logger.error(f"Error cleaning up old logs: {e}")
    
    def export_events(
        self,
        output_file: Path,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        event_type: Optional[AuditEventType] = None,
        format: str = "json"
    ):
        """Export audit events to file"""
        try:
            events = self.search_events(
                start_date=start_date,
                end_date=end_date,
                event_type=event_type,
                limit=100000
            )
            
            if format.lower() == "json":
                # Export as JSON
                events_data = []
                for event in events:
                    event_dict = asdict(event)
                    event_dict['event_type'] = event.event_type.value
                    event_dict['severity'] = event.severity.value
                    event_dict['timestamp'] = event.timestamp.isoformat()
                    events_data.append(event_dict)
                
                with open(output_file, 'w') as f:
                    json.dump(events_data, f, indent=2)
                    
            elif format.lower() == "csv":
                # Export as CSV
                import csv
                
                with open(output_file, 'w', newline='') as f:
                    if events:
                        fieldnames = ['event_id', 'event_type', 'severity', 'timestamp', 
                                    'user_id', 'session_id', 'action', 'resource', 'result']
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        
                        for event in events:
                            writer.writerow({
                                'event_id': event.event_id,
                                'event_type': event.event_type.value,
                                'severity': event.severity.value,
                                'timestamp': event.timestamp.isoformat(),
                                'user_id': event.user_id or '',
                                'session_id': event.session_id or '',
                                'action': event.action,
                                'resource': event.resource or '',
                                'result': event.result
                            })
            
            logger.info(f"Exported {len(events)} audit events to {output_file}")
            
        except Exception as e:
            logger.error(f"Error exporting audit events: {e}")
            raise
    
    def _background_logger(self):
        """Background thread for async audit logging"""
        batch = []
        last_flush = time.time()
        
        while self.is_running:
            try:
                # Get events from queue
                try:
                    event = self.event_queue.get(timeout=1.0)
                    batch.append(event)
                except queue.Empty:
                    pass
                
                # Flush batch if it's full or enough time has passed
                current_time = time.time()
                if (len(batch) >= self.batch_size or 
                    (batch and current_time - last_flush >= self.flush_interval)):
                    
                    self._flush_batch(batch)
                    batch = []
                    last_flush = current_time
                    
            except Exception as e:
                logger.error(f"Error in background audit logger: {e}")
                time.sleep(1)
        
        # Flush remaining events on shutdown
        if batch:
            self._flush_batch(batch)
    
    def _flush_batch(self, events: List[AuditEvent]):
        """Flush a batch of events to disk"""
        try:
            # Check if we need to rotate log file
            self._rotate_log_if_needed()
            
            # Write events to log file
            with open(self.current_log_file, 'a') as f:
                for event in events:
                    event_dict = asdict(event)
                    event_dict['event_type'] = event.event_type.value
                    event_dict['severity'] = event.severity.value
                    event_dict['timestamp'] = event.timestamp.isoformat()
                    
                    f.write(json.dumps(event_dict) + '\n')
            
        except Exception as e:
            logger.error(f"Error flushing audit events: {e}")
    
    def _rotate_log_if_needed(self):
        """Rotate log file if needed"""
        try:
            # Check if current log file is too large
            if (self.current_log_file.exists() and 
                self.current_log_file.stat().st_size > self.max_log_size):
                
                # Create new log file with timestamp
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                new_log_file = self.audit_dir / f"audit_{timestamp}.jsonl"
                self.current_log_file = new_log_file
                
                logger.info(f"Rotated audit log to: {new_log_file}")
            
            # Check if we need to start a new daily log
            today = datetime.now().strftime('%Y%m%d')
            expected_log_file = self.audit_dir / f"audit_{today}.jsonl"
            
            if self.current_log_file != expected_log_file:
                self.current_log_file = expected_log_file
                
        except Exception as e:
            logger.error(f"Error rotating log file: {e}")
    
    def _get_log_files_in_range(self, start_date: datetime, end_date: datetime) -> List[Path]:
        """Get log files that might contain events in the date range"""
        log_files = []
        
        # Get all audit log files
        for log_file in sorted(self.audit_dir.glob("audit_*.jsonl")):
            try:
                # Extract date from filename
                filename = log_file.stem
                if '_' in filename:
                    date_part = filename.split('_')[1]
                    if len(date_part) == 8:  # YYYYMMDD format
                        file_date = datetime.strptime(date_part, '%Y%m%d')
                        
                        # Include file if it might contain events in range
                        if (file_date.date() >= start_date.date() and 
                            file_date.date() <= end_date.date()):
                            log_files.append(log_file)
                            
            except (ValueError, IndexError):
                # Include files with non-standard names just in case
                log_files.append(log_file)
        
        return log_files
    
    def shutdown(self):
        """Shutdown audit logger"""
        self.is_running = False
        if self.logging_thread.is_alive():
            self.logging_thread.join(timeout=5.0)
        
        logger.info("Audit logger shutdown")
