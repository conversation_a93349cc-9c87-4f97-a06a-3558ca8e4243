"""
AI Engine for JARVIS AI Assistant
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
import logging

from ...config import config

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    @abstractmethod
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response from the AI model"""
        pass
    
    @abstractmethod
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ):
        """Generate a streaming response from the AI model"""
        pass


class AIEngine:
    """Main AI Engine for JARVIS"""
    
    def __init__(self):
        """Initialize the AI Engine"""
        self.provider: Optional[AIProvider] = None
        self.conversation_history: List[Dict[str, str]] = []
        self.system_prompt = self._get_system_prompt()
        self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the AI provider based on configuration"""
        provider_name = config.get("ai.provider", "openai")

        try:
            if provider_name == "openai":
                from .providers import OpenAIProvider
                self.provider = OpenAIProvider()
            elif provider_name == "anthropic":
                from .providers import AnthropicProvider
                self.provider = AnthropicProvider()
            else:
                raise ValueError(f"Unknown AI provider: {provider_name}")

            logger.info(f"Initialized AI provider: {provider_name}")
        except Exception as e:
            logger.warning(f"Failed to initialize AI provider {provider_name}: {e}")
            logger.info("Falling back to mock provider for testing")
            try:
                from .providers import MockProvider
                self.provider = MockProvider()
                logger.info("Mock provider initialized successfully")
            except Exception as mock_error:
                logger.error(f"Failed to initialize mock provider: {mock_error}")
                raise
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for JARVIS"""
        return """You are JARVIS, an advanced AI assistant with the following capabilities:

1. **Natural Language Understanding**: You can understand and respond to complex natural language queries
2. **Task Execution**: You can break down complex tasks into actionable steps
3. **System Integration**: You can interact with the operating system, files, and applications
4. **Internet Access**: You can search the web, browse websites, and gather information
5. **Memory Management**: You can remember previous conversations and user preferences
6. **Voice Interface**: You can process voice commands and respond with speech

**Core Principles:**
- Be helpful, accurate, and efficient
- Always confirm before executing potentially destructive actions
- Respect user privacy and security
- Provide clear explanations for your actions
- Ask for clarification when instructions are ambiguous

**Response Format:**
- Be concise but thorough
- Use structured responses when appropriate
- Suggest follow-up actions when relevant
- Always explain what you're doing and why

You are currently operating in a safe mode where all system operations require user confirmation."""
    
    async def process_message(
        self, 
        message: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Process a user message and generate a response
        
        Args:
            message: User's message
            context: Additional context information
            
        Returns:
            AI response
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message
            })
            
            # Prepare messages for the AI model
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (keep last 10 exchanges)
            recent_history = self.conversation_history[-20:]  # Last 10 exchanges
            messages.extend(recent_history)
            
            # Add context if provided
            if context:
                context_message = f"Additional context: {context}"
                messages.append({
                    "role": "system", 
                    "content": context_message
                })
            
            # Generate response
            response = await self.provider.generate_response(messages)
            
            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response
            })
            
            logger.info(f"Processed message: {message[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return f"I apologize, but I encountered an error: {str(e)}"
    
    async def process_streaming_message(
        self, 
        message: str, 
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Process a user message and generate a streaming response
        
        Args:
            message: User's message
            context: Additional context information
            
        Yields:
            Chunks of AI response
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message
            })
            
            # Prepare messages for the AI model
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (keep last 10 exchanges)
            recent_history = self.conversation_history[-20:]
            messages.extend(recent_history)
            
            # Add context if provided
            if context:
                context_message = f"Additional context: {context}"
                messages.append({
                    "role": "system", 
                    "content": context_message
                })
            
            # Generate streaming response
            full_response = ""
            async for chunk in self.provider.generate_streaming_response(messages):
                full_response += chunk
                yield chunk
            
            # Add complete response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": full_response
            })
            
            logger.info(f"Processed streaming message: {message[:50]}...")
            
        except Exception as e:
            logger.error(f"Error processing streaming message: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def clear_conversation(self):
        """Clear the conversation history"""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation"""
        if not self.conversation_history:
            return "No conversation history"
        
        total_messages = len(self.conversation_history)
        user_messages = len([m for m in self.conversation_history if m["role"] == "user"])
        assistant_messages = len([m for m in self.conversation_history if m["role"] == "assistant"])
        
        return f"Conversation: {total_messages} messages ({user_messages} user, {assistant_messages} assistant)"
    
    def set_system_prompt(self, prompt: str):
        """Update the system prompt"""
        self.system_prompt = prompt
        logger.info("System prompt updated")
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current AI provider"""
        if not self.provider:
            return {"provider": "None", "status": "Not initialized"}
        
        return {
            "provider": self.provider.__class__.__name__,
            "status": "Active",
            "model": getattr(self.provider, 'model', 'Unknown')
        }
