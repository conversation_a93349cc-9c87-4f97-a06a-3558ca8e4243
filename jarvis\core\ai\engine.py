"""
AI Engine for JARVIS AI Assistant
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
import logging

from ...config import config

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    @abstractmethod
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response from the AI model"""
        pass
    
    @abstractmethod
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ):
        """Generate a streaming response from the AI model"""
        pass


class AIEngine:
    """Main AI Engine for JARVIS"""
    
    def __init__(self):
        """Initialize the AI Engine"""
        self.provider: Optional[AIProvider] = None
        self.conversation_history: List[Dict[str, str]] = []
        self.system_prompt = self._get_system_prompt()
        self.nlp_processor = None
        self.task_executor = None
        self.workflow_engine = None
        self.context_manager = None
        self._initialize_provider()
        self._initialize_nlp()
        self._initialize_task_system()
        self._initialize_context_system()
    
    def _initialize_provider(self):
        """Initialize the AI provider based on configuration"""
        provider_name = config.get("ai.provider", "openai")

        try:
            if provider_name == "openai":
                from .providers import OpenAIProvider
                self.provider = OpenAIProvider()
            elif provider_name == "anthropic":
                from .providers import AnthropicProvider
                self.provider = AnthropicProvider()
            else:
                raise ValueError(f"Unknown AI provider: {provider_name}")

            logger.info(f"Initialized AI provider: {provider_name}")
        except Exception as e:
            logger.warning(f"Failed to initialize AI provider {provider_name}: {e}")
            logger.info("Falling back to mock provider for testing")
            try:
                from .providers import MockProvider
                self.provider = MockProvider()
                logger.info("Mock provider initialized successfully")
            except Exception as mock_error:
                logger.error(f"Failed to initialize mock provider: {mock_error}")
                raise

    def _initialize_nlp(self):
        """Initialize NLP processor"""
        try:
            from ..nlp import NLPProcessor
            self.nlp_processor = NLPProcessor()
            logger.info("NLP processor initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize NLP processor: {e}")
            self.nlp_processor = None

    def _initialize_task_system(self):
        """Initialize task execution and workflow systems"""
        try:
            from ..tasks import TaskExecutor
            from ..tasks.workflow import WorkflowEngine

            self.task_executor = TaskExecutor()
            self.workflow_engine = WorkflowEngine(self.task_executor)
            logger.info("Task execution system initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize task system: {e}")
            self.task_executor = None
            self.workflow_engine = None

    def _initialize_context_system(self):
        """Initialize context management system"""
        try:
            from ..context import ContextManager
            from ..memory import MemoryManager

            # Get or create memory manager
            if not hasattr(self, 'memory_manager'):
                self.memory_manager = MemoryManager()

            self.context_manager = ContextManager(self.memory_manager)
            logger.info("Context management system initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize context system: {e}")
            self.context_manager = None
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for JARVIS"""
        return """You are JARVIS, an advanced AI assistant with the following capabilities:

1. **Natural Language Understanding**: You can understand and respond to complex natural language queries
2. **Task Execution**: You can break down complex tasks into actionable steps
3. **System Integration**: You can interact with the operating system, files, and applications
4. **Internet Access**: You can search the web, browse websites, and gather information
5. **Memory Management**: You can remember previous conversations and user preferences
6. **Voice Interface**: You can process voice commands and respond with speech

**Core Principles:**
- Be helpful, accurate, and efficient
- Always confirm before executing potentially destructive actions
- Respect user privacy and security
- Provide clear explanations for your actions
- Ask for clarification when instructions are ambiguous

**Response Format:**
- Be concise but thorough
- Use structured responses when appropriate
- Suggest follow-up actions when relevant
- Always explain what you're doing and why

You are currently operating in a safe mode where all system operations require user confirmation."""
    
    async def process_message(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        session_id: str = "default"
    ) -> str:
        """
        Process a user message and generate a response

        Args:
            message: User's message
            context: Additional context information
            session_id: Session identifier for context management

        Returns:
            AI response
        """
        try:
            # Get or update conversation context
            conversation_context = None
            if self.context_manager:
                conversation_context = self.context_manager.get_context_for_message(session_id, message)
                # Merge with provided context
                if context:
                    conversation_context.update(context)
                context = conversation_context

            # Process with NLP if available
            nlp_result = None
            task_result = None

            if self.nlp_processor:
                nlp_result = self.nlp_processor.process(message, context)
                logger.debug(f"NLP analysis: intent={nlp_result.intent}, confidence={nlp_result.confidence}")

                # Update context with NLP results
                if self.context_manager:
                    self.context_manager.update_context(
                        session_id,
                        message,
                        nlp_result.intent,
                        nlp_result.entities
                    )

                # Execute task if command was identified and confidence is high enough
                if (nlp_result.command and nlp_result.confidence > 0.6 and
                    self.workflow_engine and not nlp_result.requires_confirmation):

                    try:
                        workflow = self.workflow_engine.create_nlp_workflow(nlp_result, message)
                        if workflow:
                            executed_workflow = await self.workflow_engine.execute_workflow(workflow)
                            if executed_workflow.status == "completed":
                                # Get the main task result
                                main_step_id = executed_workflow.steps[0].id if executed_workflow.steps else None
                                if main_step_id and main_step_id in executed_workflow.results:
                                    task_result = executed_workflow.results[main_step_id]
                                    logger.info(f"Task executed successfully: {nlp_result.command}")
                    except Exception as e:
                        logger.error(f"Task execution failed: {e}")

            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message
            })

            # Prepare messages for the AI model
            messages = [{"role": "system", "content": self.system_prompt}]

            # Add conversation history (keep last 10 exchanges)
            recent_history = self.conversation_history[-20:]  # Last 10 exchanges
            messages.extend(recent_history)

            # Add NLP and task execution context if available
            if nlp_result and nlp_result.intent != "unknown":
                nlp_context = f"NLP Analysis - Intent: {nlp_result.intent} (confidence: {nlp_result.confidence:.2f})"
                if nlp_result.entities:
                    nlp_context += f", Entities: {list(nlp_result.entities.keys())}"
                if nlp_result.command:
                    nlp_context += f", Suggested command: {nlp_result.command}"

                # Add task execution result if available
                if task_result:
                    if task_result.success:
                        nlp_context += f", Task executed successfully"
                        if task_result.data:
                            nlp_context += f", Result: {task_result.data}"
                    else:
                        nlp_context += f", Task execution failed: {task_result.error}"

                messages.append({
                    "role": "system",
                    "content": nlp_context
                })

            # Add additional context if provided
            if context:
                context_message = f"Additional context: {context}"
                messages.append({
                    "role": "system",
                    "content": context_message
                })

            # Generate response
            response = await self.provider.generate_response(messages)

            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response
            })

            logger.info(f"Processed message: {message[:50]}...")
            return response

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return f"I apologize, but I encountered an error: {str(e)}"
    
    async def process_streaming_message(
        self, 
        message: str, 
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Process a user message and generate a streaming response
        
        Args:
            message: User's message
            context: Additional context information
            
        Yields:
            Chunks of AI response
        """
        try:
            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message
            })
            
            # Prepare messages for the AI model
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (keep last 10 exchanges)
            recent_history = self.conversation_history[-20:]
            messages.extend(recent_history)
            
            # Add context if provided
            if context:
                context_message = f"Additional context: {context}"
                messages.append({
                    "role": "system", 
                    "content": context_message
                })
            
            # Generate streaming response
            full_response = ""
            async for chunk in self.provider.generate_streaming_response(messages):
                full_response += chunk
                yield chunk
            
            # Add complete response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": full_response
            })
            
            logger.info(f"Processed streaming message: {message[:50]}...")
            
        except Exception as e:
            logger.error(f"Error processing streaming message: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def clear_conversation(self):
        """Clear the conversation history"""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation"""
        if not self.conversation_history:
            return "No conversation history"
        
        total_messages = len(self.conversation_history)
        user_messages = len([m for m in self.conversation_history if m["role"] == "user"])
        assistant_messages = len([m for m in self.conversation_history if m["role"] == "assistant"])
        
        return f"Conversation: {total_messages} messages ({user_messages} user, {assistant_messages} assistant)"
    
    def set_system_prompt(self, prompt: str):
        """Update the system prompt"""
        self.system_prompt = prompt
        logger.info("System prompt updated")
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current AI provider"""
        if not self.provider:
            return {"provider": "None", "status": "Not initialized"}
        
        return {
            "provider": self.provider.__class__.__name__,
            "status": "Active",
            "model": getattr(self.provider, 'model', 'Unknown')
        }
