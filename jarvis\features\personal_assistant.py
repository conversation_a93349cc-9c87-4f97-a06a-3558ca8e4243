"""
Personal Assistant Features for JARVIS AI Assistant
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
from pathlib import Path
import asyncio

from ...config import config

logger = logging.getLogger(__name__)


@dataclass
class Task:
    """Personal task representation"""
    task_id: str
    title: str
    description: str
    priority: str = "medium"  # low, medium, high, urgent
    status: str = "pending"  # pending, in_progress, completed, cancelled
    due_date: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    subtasks: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Reminder:
    """Reminder representation"""
    reminder_id: str
    message: str
    remind_at: datetime
    created_at: datetime = field(default_factory=datetime.now)
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = None  # daily, weekly, monthly
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CalendarEvent:
    """Calendar event representation"""
    event_id: str
    title: str
    description: str
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    attendees: List[str] = field(default_factory=list)
    is_all_day: bool = False
    reminder_minutes: int = 15
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class PersonalAssistant:
    """Advanced personal assistant with task management, reminders, and calendar"""
    
    def __init__(self, user_id: str, data_dir: Optional[str] = None):
        """Initialize personal assistant"""
        self.user_id = user_id
        self.data_dir = Path(data_dir or config.get("data_dir", "data"))
        
        # Data files
        self.tasks_file = self.data_dir / f"tasks_{user_id}.json"
        self.reminders_file = self.data_dir / f"reminders_{user_id}.json"
        self.calendar_file = self.data_dir / f"calendar_{user_id}.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Data storage
        self.tasks: Dict[str, Task] = {}
        self.reminders: Dict[str, Reminder] = {}
        self.calendar_events: Dict[str, CalendarEvent] = {}
        
        # Load existing data
        self._load_data()
        
        logger.info(f"Personal assistant initialized for user {user_id}")
    
    # Task Management
    async def create_task(
        self,
        title: str,
        description: str = "",
        priority: str = "medium",
        due_date: Optional[datetime] = None,
        tags: Optional[List[str]] = None
    ) -> str:
        """Create a new task"""
        import uuid
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            title=title,
            description=description,
            priority=priority,
            due_date=due_date,
            tags=tags or []
        )
        
        self.tasks[task_id] = task
        await self._save_tasks()
        
        logger.info(f"Created task: {title}")
        return task_id
    
    async def update_task(
        self,
        task_id: str,
        **updates
    ) -> bool:
        """Update an existing task"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        # Update allowed fields
        allowed_fields = ['title', 'description', 'priority', 'status', 'due_date', 'tags']
        for field, value in updates.items():
            if field in allowed_fields:
                setattr(task, field, value)
        
        task.updated_at = datetime.now()
        await self._save_tasks()
        
        logger.info(f"Updated task {task_id}")
        return True
    
    async def complete_task(self, task_id: str) -> bool:
        """Mark a task as completed"""
        return await self.update_task(task_id, status="completed")
    
    async def delete_task(self, task_id: str) -> bool:
        """Delete a task"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            await self._save_tasks()
            logger.info(f"Deleted task {task_id}")
            return True
        return False
    
    def get_tasks(
        self,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        tag: Optional[str] = None,
        due_soon: bool = False
    ) -> List[Task]:
        """Get tasks with optional filtering"""
        tasks = list(self.tasks.values())
        
        # Apply filters
        if status:
            tasks = [t for t in tasks if t.status == status]
        
        if priority:
            tasks = [t for t in tasks if t.priority == priority]
        
        if tag:
            tasks = [t for t in tasks if tag in t.tags]
        
        if due_soon:
            soon_threshold = datetime.now() + timedelta(days=3)
            tasks = [t for t in tasks if t.due_date and t.due_date <= soon_threshold]
        
        # Sort by priority and due date
        priority_order = {"urgent": 0, "high": 1, "medium": 2, "low": 3}
        tasks.sort(key=lambda t: (
            priority_order.get(t.priority, 4),
            t.due_date or datetime.max,
            t.created_at
        ))
        
        return tasks
    
    def get_task_summary(self) -> Dict[str, Any]:
        """Get task summary statistics"""
        all_tasks = list(self.tasks.values())
        
        status_counts = {}
        priority_counts = {}
        
        for task in all_tasks:
            status_counts[task.status] = status_counts.get(task.status, 0) + 1
            priority_counts[task.priority] = priority_counts.get(task.priority, 0) + 1
        
        # Count overdue tasks
        now = datetime.now()
        overdue_count = sum(
            1 for task in all_tasks 
            if task.due_date and task.due_date < now and task.status != "completed"
        )
        
        return {
            "total_tasks": len(all_tasks),
            "status_breakdown": status_counts,
            "priority_breakdown": priority_counts,
            "overdue_tasks": overdue_count,
            "completed_today": len([
                t for t in all_tasks 
                if t.status == "completed" and t.updated_at.date() == now.date()
            ])
        }
    
    # Reminder Management
    async def create_reminder(
        self,
        message: str,
        remind_at: datetime,
        is_recurring: bool = False,
        recurrence_pattern: Optional[str] = None
    ) -> str:
        """Create a new reminder"""
        import uuid
        reminder_id = str(uuid.uuid4())
        
        reminder = Reminder(
            reminder_id=reminder_id,
            message=message,
            remind_at=remind_at,
            is_recurring=is_recurring,
            recurrence_pattern=recurrence_pattern
        )
        
        self.reminders[reminder_id] = reminder
        await self._save_reminders()
        
        logger.info(f"Created reminder: {message} at {remind_at}")
        return reminder_id
    
    async def snooze_reminder(self, reminder_id: str, minutes: int = 10) -> bool:
        """Snooze a reminder"""
        if reminder_id not in self.reminders:
            return False
        
        reminder = self.reminders[reminder_id]
        reminder.remind_at = datetime.now() + timedelta(minutes=minutes)
        await self._save_reminders()
        
        logger.info(f"Snoozed reminder {reminder_id} for {minutes} minutes")
        return True
    
    async def dismiss_reminder(self, reminder_id: str) -> bool:
        """Dismiss a reminder"""
        if reminder_id not in self.reminders:
            return False
        
        reminder = self.reminders[reminder_id]
        
        if reminder.is_recurring:
            # Schedule next occurrence
            next_time = self._calculate_next_recurrence(reminder.remind_at, reminder.recurrence_pattern)
            if next_time:
                reminder.remind_at = next_time
            else:
                reminder.is_active = False
        else:
            reminder.is_active = False
        
        await self._save_reminders()
        logger.info(f"Dismissed reminder {reminder_id}")
        return True
    
    def get_active_reminders(self) -> List[Reminder]:
        """Get all active reminders"""
        now = datetime.now()
        return [
            reminder for reminder in self.reminders.values()
            if reminder.is_active and reminder.remind_at <= now
        ]
    
    def get_upcoming_reminders(self, hours: int = 24) -> List[Reminder]:
        """Get upcoming reminders within specified hours"""
        now = datetime.now()
        future_time = now + timedelta(hours=hours)
        
        return [
            reminder for reminder in self.reminders.values()
            if reminder.is_active and now < reminder.remind_at <= future_time
        ]
    
    # Calendar Management
    async def create_event(
        self,
        title: str,
        start_time: datetime,
        end_time: datetime,
        description: str = "",
        location: Optional[str] = None,
        attendees: Optional[List[str]] = None
    ) -> str:
        """Create a calendar event"""
        import uuid
        event_id = str(uuid.uuid4())
        
        event = CalendarEvent(
            event_id=event_id,
            title=title,
            description=description,
            start_time=start_time,
            end_time=end_time,
            location=location,
            attendees=attendees or []
        )
        
        self.calendar_events[event_id] = event
        await self._save_calendar()
        
        logger.info(f"Created calendar event: {title}")
        return event_id
    
    def get_events_for_date(self, date: datetime) -> List[CalendarEvent]:
        """Get events for a specific date"""
        target_date = date.date()
        
        return [
            event for event in self.calendar_events.values()
            if event.start_time.date() == target_date
        ]
    
    def get_events_for_range(self, start_date: datetime, end_date: datetime) -> List[CalendarEvent]:
        """Get events within a date range"""
        return [
            event for event in self.calendar_events.values()
            if start_date <= event.start_time <= end_date
        ]
    
    def get_next_event(self) -> Optional[CalendarEvent]:
        """Get the next upcoming event"""
        now = datetime.now()
        upcoming_events = [
            event for event in self.calendar_events.values()
            if event.start_time > now
        ]
        
        if upcoming_events:
            return min(upcoming_events, key=lambda e: e.start_time)
        return None
    
    # Daily Summary and Planning
    def get_daily_summary(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get daily summary of tasks, reminders, and events"""
        if not date:
            date = datetime.now()
        
        target_date = date.date()
        
        # Get tasks due today
        tasks_due = [
            task for task in self.tasks.values()
            if task.due_date and task.due_date.date() == target_date
        ]
        
        # Get events for today
        events_today = self.get_events_for_date(date)
        
        # Get reminders for today
        reminders_today = [
            reminder for reminder in self.reminders.values()
            if reminder.is_active and reminder.remind_at.date() == target_date
        ]
        
        return {
            "date": target_date.isoformat(),
            "tasks_due": len(tasks_due),
            "tasks_due_list": [{"id": t.task_id, "title": t.title, "priority": t.priority} for t in tasks_due],
            "events_count": len(events_today),
            "events_list": [{"id": e.event_id, "title": e.title, "time": e.start_time.strftime("%H:%M")} for e in events_today],
            "reminders_count": len(reminders_today),
            "reminders_list": [{"id": r.reminder_id, "message": r.message, "time": r.remind_at.strftime("%H:%M")} for r in reminders_today]
        }
    
    def get_weekly_overview(self) -> Dict[str, Any]:
        """Get weekly overview"""
        now = datetime.now()
        week_start = now - timedelta(days=now.weekday())
        week_end = week_start + timedelta(days=6)
        
        # Get events for the week
        week_events = self.get_events_for_range(week_start, week_end)
        
        # Get tasks due this week
        week_tasks = [
            task for task in self.tasks.values()
            if task.due_date and week_start.date() <= task.due_date.date() <= week_end.date()
        ]
        
        return {
            "week_start": week_start.date().isoformat(),
            "week_end": week_end.date().isoformat(),
            "total_events": len(week_events),
            "total_tasks_due": len(week_tasks),
            "high_priority_tasks": len([t for t in week_tasks if t.priority in ["high", "urgent"]]),
            "events_by_day": self._group_events_by_day(week_events),
            "tasks_by_day": self._group_tasks_by_day(week_tasks)
        }
    
    # Smart Suggestions
    def get_smart_suggestions(self) -> List[Dict[str, Any]]:
        """Get smart suggestions based on current state"""
        suggestions = []
        
        # Overdue task suggestions
        overdue_tasks = [
            task for task in self.tasks.values()
            if task.due_date and task.due_date < datetime.now() and task.status != "completed"
        ]
        
        if overdue_tasks:
            suggestions.append({
                "type": "overdue_tasks",
                "message": f"You have {len(overdue_tasks)} overdue tasks",
                "action": "review_overdue_tasks",
                "priority": "high"
            })
        
        # Upcoming events
        next_event = self.get_next_event()
        if next_event:
            time_until = next_event.start_time - datetime.now()
            if time_until.total_seconds() < 3600:  # Within 1 hour
                suggestions.append({
                    "type": "upcoming_event",
                    "message": f"'{next_event.title}' starts in {int(time_until.total_seconds() / 60)} minutes",
                    "action": "prepare_for_event",
                    "priority": "medium"
                })
        
        # Task completion suggestions
        pending_tasks = self.get_tasks(status="pending")
        if len(pending_tasks) > 10:
            suggestions.append({
                "type": "task_management",
                "message": f"You have {len(pending_tasks)} pending tasks. Consider prioritizing.",
                "action": "organize_tasks",
                "priority": "low"
            })
        
        return suggestions
    
    # Helper methods
    def _calculate_next_recurrence(self, current_time: datetime, pattern: str) -> Optional[datetime]:
        """Calculate next recurrence time"""
        if pattern == "daily":
            return current_time + timedelta(days=1)
        elif pattern == "weekly":
            return current_time + timedelta(weeks=1)
        elif pattern == "monthly":
            # Simple monthly recurrence (same day next month)
            try:
                if current_time.month == 12:
                    return current_time.replace(year=current_time.year + 1, month=1)
                else:
                    return current_time.replace(month=current_time.month + 1)
            except ValueError:
                # Handle edge cases like Feb 29
                return current_time + timedelta(days=30)
        
        return None
    
    def _group_events_by_day(self, events: List[CalendarEvent]) -> Dict[str, int]:
        """Group events by day of week"""
        day_counts = {}
        for event in events:
            day = event.start_time.strftime("%A")
            day_counts[day] = day_counts.get(day, 0) + 1
        return day_counts
    
    def _group_tasks_by_day(self, tasks: List[Task]) -> Dict[str, int]:
        """Group tasks by due date day"""
        day_counts = {}
        for task in tasks:
            if task.due_date:
                day = task.due_date.strftime("%A")
                day_counts[day] = day_counts.get(day, 0) + 1
        return day_counts
    
    # Data persistence
    async def _save_tasks(self):
        """Save tasks to file"""
        try:
            tasks_data = {}
            for task_id, task in self.tasks.items():
                task_dict = {
                    "task_id": task.task_id,
                    "title": task.title,
                    "description": task.description,
                    "priority": task.priority,
                    "status": task.status,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "created_at": task.created_at.isoformat(),
                    "updated_at": task.updated_at.isoformat(),
                    "tags": task.tags,
                    "subtasks": task.subtasks,
                    "dependencies": task.dependencies,
                    "metadata": task.metadata
                }
                tasks_data[task_id] = task_dict
            
            with open(self.tasks_file, 'w') as f:
                json.dump(tasks_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving tasks: {e}")
    
    async def _save_reminders(self):
        """Save reminders to file"""
        try:
            reminders_data = {}
            for reminder_id, reminder in self.reminders.items():
                reminder_dict = {
                    "reminder_id": reminder.reminder_id,
                    "message": reminder.message,
                    "remind_at": reminder.remind_at.isoformat(),
                    "created_at": reminder.created_at.isoformat(),
                    "is_recurring": reminder.is_recurring,
                    "recurrence_pattern": reminder.recurrence_pattern,
                    "is_active": reminder.is_active,
                    "metadata": reminder.metadata
                }
                reminders_data[reminder_id] = reminder_dict
            
            with open(self.reminders_file, 'w') as f:
                json.dump(reminders_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving reminders: {e}")
    
    async def _save_calendar(self):
        """Save calendar events to file"""
        try:
            calendar_data = {}
            for event_id, event in self.calendar_events.items():
                event_dict = {
                    "event_id": event.event_id,
                    "title": event.title,
                    "description": event.description,
                    "start_time": event.start_time.isoformat(),
                    "end_time": event.end_time.isoformat(),
                    "location": event.location,
                    "attendees": event.attendees,
                    "is_all_day": event.is_all_day,
                    "reminder_minutes": event.reminder_minutes,
                    "created_at": event.created_at.isoformat(),
                    "metadata": event.metadata
                }
                calendar_data[event_id] = event_dict
            
            with open(self.calendar_file, 'w') as f:
                json.dump(calendar_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving calendar: {e}")
    
    def _load_data(self):
        """Load all data from files"""
        self._load_tasks()
        self._load_reminders()
        self._load_calendar()
    
    def _load_tasks(self):
        """Load tasks from file"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r') as f:
                    tasks_data = json.load(f)
                
                for task_id, task_dict in tasks_data.items():
                    # Convert datetime strings back to datetime objects
                    if task_dict.get("due_date"):
                        task_dict["due_date"] = datetime.fromisoformat(task_dict["due_date"])
                    task_dict["created_at"] = datetime.fromisoformat(task_dict["created_at"])
                    task_dict["updated_at"] = datetime.fromisoformat(task_dict["updated_at"])
                    
                    task = Task(**task_dict)
                    self.tasks[task_id] = task
                
                logger.info(f"Loaded {len(self.tasks)} tasks")
                
        except Exception as e:
            logger.error(f"Error loading tasks: {e}")
    
    def _load_reminders(self):
        """Load reminders from file"""
        try:
            if self.reminders_file.exists():
                with open(self.reminders_file, 'r') as f:
                    reminders_data = json.load(f)
                
                for reminder_id, reminder_dict in reminders_data.items():
                    # Convert datetime strings back to datetime objects
                    reminder_dict["remind_at"] = datetime.fromisoformat(reminder_dict["remind_at"])
                    reminder_dict["created_at"] = datetime.fromisoformat(reminder_dict["created_at"])
                    
                    reminder = Reminder(**reminder_dict)
                    self.reminders[reminder_id] = reminder
                
                logger.info(f"Loaded {len(self.reminders)} reminders")
                
        except Exception as e:
            logger.error(f"Error loading reminders: {e}")
    
    def _load_calendar(self):
        """Load calendar events from file"""
        try:
            if self.calendar_file.exists():
                with open(self.calendar_file, 'r') as f:
                    calendar_data = json.load(f)
                
                for event_id, event_dict in calendar_data.items():
                    # Convert datetime strings back to datetime objects
                    event_dict["start_time"] = datetime.fromisoformat(event_dict["start_time"])
                    event_dict["end_time"] = datetime.fromisoformat(event_dict["end_time"])
                    event_dict["created_at"] = datetime.fromisoformat(event_dict["created_at"])
                    
                    event = CalendarEvent(**event_dict)
                    self.calendar_events[event_id] = event
                
                logger.info(f"Loaded {len(self.calendar_events)} calendar events")
                
        except Exception as e:
            logger.error(f"Error loading calendar: {e}")
