"""
Memory Manager for JARVIS AI Assistant
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

from ...config import config

logger = logging.getLogger(__name__)


class MemoryManager:
    """Manages different types of memory for JARVIS"""
    
    def __init__(self):
        """Initialize the memory manager"""
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize storage components
        from .storage import ConversationStorage, PersonalStorage
        self.conversation_storage = ConversationStorage(self.data_dir)
        self.personal_storage = PersonalStorage(self.data_dir)
        self.vector_storage = None  # Will be initialized if needed
        
        logger.info("Memory manager initialized")
    
    def store_conversation(
        self, 
        user_message: str, 
        assistant_response: str, 
        context: Optional[Dict[str, Any]] = None
    ):
        """Store a conversation exchange"""
        self.conversation_storage.add_exchange(
            user_message, 
            assistant_response, 
            context
        )
    
    def get_conversation_history(
        self, 
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get conversation history"""
        return self.conversation_storage.get_history(limit)
    
    def search_conversations(self, query: str) -> List[Dict[str, Any]]:
        """Search through conversation history"""
        return self.conversation_storage.search(query)
    
    def store_personal_info(self, key: str, value: Any):
        """Store personal information"""
        self.personal_storage.set(key, value)
    
    def get_personal_info(self, key: str, default: Any = None) -> Any:
        """Get personal information"""
        return self.personal_storage.get(key, default)
    
    def update_personal_info(self, data: Dict[str, Any]):
        """Update multiple personal information items"""
        self.personal_storage.update(data)
    
    def get_all_personal_info(self) -> Dict[str, Any]:
        """Get all personal information"""
        return self.personal_storage.get_all()
    
    def clear_conversation_history(self):
        """Clear all conversation history"""
        self.conversation_storage.clear()
    
    def clear_personal_info(self):
        """Clear all personal information"""
        self.personal_storage.clear()
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        return {
            "conversations": self.conversation_storage.get_stats(),
            "personal_info": self.personal_storage.get_stats(),
            "data_directory": str(self.data_dir),
            "total_size_mb": self._get_total_size()
        }
    
    def _get_total_size(self) -> float:
        """Get total size of memory data in MB"""
        total_size = 0
        for file_path in self.data_dir.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return round(total_size / (1024 * 1024), 2)



