"""
AI Provider implementations for JARVIS AI Assistant
"""

import asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator
import logging

from .engine import AIProvider
from ...config import config

logger = logging.getLogger(__name__)


class OpenAIProvider(AIProvider):
    """OpenAI API provider"""
    
    def __init__(self):
        """Initialize OpenAI provider"""
        try:
            import openai
            self.client = openai.AsyncOpenAI(
                api_key=config.get_api_key("openai")
            )
            self.model = config.get("ai.openai.model", "gpt-4")
            self.temperature = config.get("ai.openai.temperature", 0.7)
            self.max_tokens = config.get("ai.openai.max_tokens", 2000)
            logger.info(f"OpenAI provider initialized with model: {self.model}")
        except ImportError:
            raise ImportError("OpenAI library not installed. Run: pip install openai")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            raise
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response using OpenAI API"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                **kwargs
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using OpenAI API"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI streaming API error: {e}")
            raise


class AnthropicProvider(AIProvider):
    """Anthropic Claude API provider"""
    
    def __init__(self):
        """Initialize Anthropic provider"""
        try:
            import anthropic
            self.client = anthropic.AsyncAnthropic(
                api_key=config.get_api_key("anthropic")
            )
            self.model = config.get("ai.anthropic.model", "claude-3-sonnet-20240229")
            self.temperature = config.get("ai.anthropic.temperature", 0.7)
            self.max_tokens = config.get("ai.anthropic.max_tokens", 2000)
            logger.info(f"Anthropic provider initialized with model: {self.model}")
        except ImportError:
            raise ImportError("Anthropic library not installed. Run: pip install anthropic")
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic provider: {e}")
            raise
    
    def _convert_messages(self, messages: List[Dict[str, str]]) -> tuple:
        """Convert messages to Anthropic format"""
        system_message = ""
        conversation_messages = []
        
        for message in messages:
            if message["role"] == "system":
                system_message += message["content"] + "\n"
            else:
                conversation_messages.append(message)
        
        return system_message.strip(), conversation_messages
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response using Anthropic API"""
        try:
            system_message, conversation_messages = self._convert_messages(messages)
            
            response = await self.client.messages.create(
                model=self.model,
                system=system_message,
                messages=conversation_messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                **kwargs
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using Anthropic API"""
        try:
            system_message, conversation_messages = self._convert_messages(messages)
            
            stream = await self.client.messages.create(
                model=self.model,
                system=system_message,
                messages=conversation_messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Anthropic streaming API error: {e}")
            raise


class MockProvider(AIProvider):
    """Mock AI provider for testing without API keys"""

    def __init__(self):
        """Initialize mock provider"""
        self.model = "mock-model"
        logger.info("Mock AI provider initialized for testing")

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """Generate a mock response"""
        user_message = ""
        for msg in messages:
            if msg["role"] == "user":
                user_message = msg["content"]
                break

        # Simple mock responses based on keywords
        user_lower = user_message.lower()

        if "hello" in user_lower or "hi" in user_lower:
            return "Hello! I'm JARVIS, your AI assistant. I'm currently running in test mode without API access. How can I help you today?"
        elif "help" in user_lower:
            return "I can help you with various tasks! Try asking me about:\n- System information (/status command)\n- Time and date\n- Basic calculations\n- File operations (when configured)\n\nNote: I'm currently in test mode, so some features may be limited."
        elif "time" in user_lower:
            from datetime import datetime
            return f"The current time is {datetime.now().strftime('%H:%M:%S')}"
        elif "date" in user_lower:
            from datetime import datetime
            return f"Today's date is {datetime.now().strftime('%Y-%m-%d')}"
        elif "weather" in user_lower:
            return "I'd love to help with weather information, but I need internet access and API keys to fetch real weather data. Currently running in test mode."
        elif "calculate" in user_lower or "math" in user_lower:
            return "I can help with calculations! Try asking me something like 'What is 2 + 2?' or 'Calculate 15 * 7'."
        elif any(op in user_lower for op in ['+', '-', '*', '/', 'plus', 'minus', 'times', 'divided']):
            try:
                # Simple math evaluation (be careful in production!)
                import re
                numbers = re.findall(r'\d+', user_message)
                if len(numbers) >= 2:
                    if '+' in user_message or 'plus' in user_lower:
                        result = int(numbers[0]) + int(numbers[1])
                        return f"The result is {result}"
                    elif '-' in user_message or 'minus' in user_lower:
                        result = int(numbers[0]) - int(numbers[1])
                        return f"The result is {result}"
                    elif '*' in user_message or 'times' in user_lower:
                        result = int(numbers[0]) * int(numbers[1])
                        return f"The result is {result}"
                    elif '/' in user_message or 'divided' in user_lower:
                        if int(numbers[1]) != 0:
                            result = int(numbers[0]) / int(numbers[1])
                            return f"The result is {result}"
                        else:
                            return "I can't divide by zero!"
            except:
                pass
            return "I can help with basic math! Try asking something like 'What is 5 + 3?' or 'Calculate 10 times 4'."
        else:
            return f"I understand you said: '{user_message}'\n\nI'm currently running in test mode without full AI capabilities. To unlock my full potential, please configure an OpenAI API key in the configuration file. For now, I can help with basic commands and system information!"

    async def generate_streaming_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming mock response"""
        response = await self.generate_response(messages, **kwargs)

        # Simulate streaming by yielding chunks
        import asyncio
        words = response.split()
        for i, word in enumerate(words):
            if i == 0:
                yield word
            else:
                yield " " + word
            await asyncio.sleep(0.05)  # Small delay to simulate streaming


class LocalProvider(AIProvider):
    """Local model provider (placeholder for future implementation)"""

    def __init__(self):
        """Initialize local provider"""
        self.model_path = config.get("ai.local.model_path")
        self.device = config.get("ai.local.device", "auto")
        logger.warning("Local provider not yet implemented")
        raise NotImplementedError("Local provider not yet implemented")

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """Generate a response using local model"""
        raise NotImplementedError("Local provider not yet implemented")

    async def generate_streaming_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using local model"""
        raise NotImplementedError("Local provider not yet implemented")
        yield  # Make this a generator
