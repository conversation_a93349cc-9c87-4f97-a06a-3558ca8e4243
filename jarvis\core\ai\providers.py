"""
AI Provider implementations for JARVIS AI Assistant
"""

import asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator
import logging

from .engine import AIProvider
from ...config import config

logger = logging.getLogger(__name__)


class OpenAIProvider(AIProvider):
    """OpenAI API provider"""
    
    def __init__(self):
        """Initialize OpenAI provider"""
        try:
            import openai
            self.client = openai.AsyncOpenAI(
                api_key=config.get_api_key("openai")
            )
            self.model = config.get("ai.openai.model", "gpt-4")
            self.temperature = config.get("ai.openai.temperature", 0.7)
            self.max_tokens = config.get("ai.openai.max_tokens", 2000)
            logger.info(f"OpenAI provider initialized with model: {self.model}")
        except ImportError:
            raise ImportError("OpenAI library not installed. Run: pip install openai")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            raise
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response using OpenAI API"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                **kwargs
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using OpenAI API"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI streaming API error: {e}")
            raise


class AnthropicProvider(AIProvider):
    """Anthropic Claude API provider"""
    
    def __init__(self):
        """Initialize Anthropic provider"""
        try:
            import anthropic
            self.client = anthropic.AsyncAnthropic(
                api_key=config.get_api_key("anthropic")
            )
            self.model = config.get("ai.anthropic.model", "claude-3-sonnet-20240229")
            self.temperature = config.get("ai.anthropic.temperature", 0.7)
            self.max_tokens = config.get("ai.anthropic.max_tokens", 2000)
            logger.info(f"Anthropic provider initialized with model: {self.model}")
        except ImportError:
            raise ImportError("Anthropic library not installed. Run: pip install anthropic")
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic provider: {e}")
            raise
    
    def _convert_messages(self, messages: List[Dict[str, str]]) -> tuple:
        """Convert messages to Anthropic format"""
        system_message = ""
        conversation_messages = []
        
        for message in messages:
            if message["role"] == "system":
                system_message += message["content"] + "\n"
            else:
                conversation_messages.append(message)
        
        return system_message.strip(), conversation_messages
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response using Anthropic API"""
        try:
            system_message, conversation_messages = self._convert_messages(messages)
            
            response = await self.client.messages.create(
                model=self.model,
                system=system_message,
                messages=conversation_messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                **kwargs
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using Anthropic API"""
        try:
            system_message, conversation_messages = self._convert_messages(messages)
            
            stream = await self.client.messages.create(
                model=self.model,
                system=system_message,
                messages=conversation_messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Anthropic streaming API error: {e}")
            raise


class LocalProvider(AIProvider):
    """Local model provider (placeholder for future implementation)"""
    
    def __init__(self):
        """Initialize local provider"""
        self.model_path = config.get("ai.local.model_path")
        self.device = config.get("ai.local.device", "auto")
        logger.warning("Local provider not yet implemented")
        raise NotImplementedError("Local provider not yet implemented")
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate a response using local model"""
        raise NotImplementedError("Local provider not yet implemented")
    
    async def generate_streaming_response(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response using local model"""
        raise NotImplementedError("Local provider not yet implemented")
        yield  # Make this a generator
