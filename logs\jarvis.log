2025-07-11 12:25:45,468 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:25:45,469 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:25:46,164 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:25:46,165 - jarvis.core.ai.engine - ERROR - Failed to initialize AI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:46,568 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:26:46,571 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:26:47,039 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:47,039 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:26:47,040 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:26:47,040 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:26:47,040 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:26:47,041 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:26:47,041 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:27:05,487 - jarvis.core.ai.engine - INFO - Processed message: hello...
2025-07-11 12:27:37,764 - jarvis.core.ai.engine - INFO - Processed message: what is 5 + 3?...
2025-07-11 12:27:52,865 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:27:52,865 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:34:35,801 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:34:35,801 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:34:36,518 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:34:36,519 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:34:36,519 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:34:36,520 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:34:36,520 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:34:36,536 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:34:36,536 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:34:36,537 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:34:36,537 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:34:36,537 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:34:36,538 - jarvis.core.memory.storage - INFO - Loaded 2 conversations
2025-07-11 12:34:36,538 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:34:36,539 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:34:54,135 - jarvis.core.ai.engine - INFO - Processed message: what time is it?...
2025-07-11 12:35:30,417 - jarvis.core.ai.engine - INFO - Processed message: calculate 15 + 7...
2025-07-11 12:35:47,402 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:35:47,403 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:38:46,307 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:38:46,307 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:38:46,745 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:38:46,745 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:38:46,745 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:38:46,746 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:38:46,746 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:38:46,750 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:38:46,751 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:38:46,752 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:38:46,757 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:38:46,761 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:38:46,762 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:38:46,763 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:38:46,763 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:38:46,763 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:38:46,764 - jarvis.core.memory.storage - INFO - Loaded 4 conversations
2025-07-11 12:38:46,764 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:38:46,764 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:39:05,568 - jarvis.core.ai.engine - INFO - Processed message: calculate 15 + 7...
2025-07-11 12:39:28,022 - jarvis.core.ai.engine - INFO - Processed message: what time is it?...
2025-07-11 12:40:02,899 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:40:02,900 - __main__ - INFO - JARVIS AI Assistant stopped
2025-07-11 12:44:17,068 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:44:17,068 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:44:17,497 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:44:17,497 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:44:17,497 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:44:17,501 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:44:17,501 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:44:17,502 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:44:17,502 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:44:17,502 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:44:17,504 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:44:17,506 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:44:17,506 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:44:17,506 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:44:17,511 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 12:44:17,515 - jarvis.core.memory.storage - INFO - Loaded 6 conversations
2025-07-11 12:44:17,515 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 12:44:17,516 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 12:44:17,516 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 12:44:17,516 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 12:44:17,516 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:44:17,517 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:44:31,937 - jarvis.core.ai.engine - INFO - Processed message: hello...
2025-07-11 12:44:59,003 - jarvis.utils.logging - INFO - Logging configured - Level: INFO, File: logs/jarvis.log
2025-07-11 12:44:59,004 - __main__ - INFO - Starting JARVIS AI Assistant
2025-07-11 12:44:59,399 - jarvis.core.ai.providers - ERROR - Failed to initialize OpenAI provider: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:59,399 - jarvis.core.ai.engine - WARNING - Failed to initialize AI provider openai: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable
2025-07-11 12:44:59,400 - jarvis.core.ai.engine - INFO - Falling back to mock provider for testing
2025-07-11 12:44:59,400 - jarvis.core.ai.providers - INFO - Mock AI provider initialized for testing
2025-07-11 12:44:59,400 - jarvis.core.ai.engine - INFO - Mock provider initialized successfully
2025-07-11 12:44:59,403 - jarvis.core.nlp.intent_recognition - INFO - Intent recognizer initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.entity_extraction - INFO - Entity extractor initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.command_parser - INFO - Command parser initialized
2025-07-11 12:44:59,404 - jarvis.core.nlp.processor - INFO - NLP Processor initialized
2025-07-11 12:44:59,405 - jarvis.core.ai.engine - INFO - NLP processor initialized
2025-07-11 12:44:59,406 - jarvis.core.tasks.executor - INFO - Registered task handler: system
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: math
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: weather
2025-07-11 12:44:59,408 - jarvis.core.tasks.executor - INFO - Registered task handler: file
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered task handler: web
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered task handler: reminder
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Registered advanced task handlers
2025-07-11 12:44:59,409 - jarvis.core.tasks.executor - INFO - Task executor initialized with 4 workers
2025-07-11 12:44:59,410 - jarvis.core.tasks.workflow - INFO - Workflow engine initialized
2025-07-11 12:44:59,410 - jarvis.core.ai.engine - INFO - Task execution system initialized
2025-07-11 12:44:59,411 - jarvis.core.ai.engine - WARNING - Failed to initialize context system: No module named 'jarvis.core.context.session'
2025-07-11 12:44:59,412 - jarvis.core.memory.storage - INFO - Loaded 7 conversations
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - WARNING - ChromaDB not available, using fallback storage
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - WARNING - sentence-transformers not available, using simple embeddings
2025-07-11 12:44:59,413 - jarvis.core.memory.vector_storage - INFO - Vector storage initialized
2025-07-11 12:44:59,413 - jarvis.core.memory.manager - INFO - Vector storage initialized
2025-07-11 12:44:59,413 - jarvis.core.memory.manager - INFO - Memory manager initialized
2025-07-11 12:44:59,414 - jarvis.interfaces.cli.interface - INFO - CLI interface initialized
2025-07-11 12:46:12,603 - jarvis.interfaces.cli.interface - INFO - CLI interface stopped
2025-07-11 12:46:12,603 - __main__ - INFO - JARVIS AI Assistant stopped
