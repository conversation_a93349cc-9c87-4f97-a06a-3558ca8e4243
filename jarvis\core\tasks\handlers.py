"""
Advanced Task Handlers for JARVIS AI Assistant
"""

import asyncio
import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import math

from .base import TaskHandler, Task, TaskResult

logger = logging.getLogger(__name__)


class MathTaskHandler(TaskHandler):
    """Handler for mathematical calculations"""
    
    @property
    def name(self) -> str:
        return "math"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a math task"""
        return task.command.startswith("math.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a math calculation task"""
        try:
            command = task.command.replace("math.", "")
            
            if command == "calculate":
                return await self._calculate(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown math command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Math task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _calculate(self, task: Task) -> TaskResult:
        """Perform mathematical calculation"""
        try:
            # Get expression from parameters
            expression = task.parameters.get("expression")
            if not expression:
                # Try to extract from original text
                original_text = task.parameters.get("original_text", "")
                expression = self._extract_math_expression(original_text)
            
            if not expression:
                return TaskResult(
                    success=False,
                    error="No mathematical expression found"
                )
            
            # Clean and validate expression
            cleaned_expr = self._clean_expression(expression)
            if not self._is_safe_expression(cleaned_expr):
                return TaskResult(
                    success=False,
                    error="Invalid or unsafe mathematical expression"
                )
            
            # Calculate result
            result = eval(cleaned_expr)
            
            return TaskResult(
                success=True,
                data={
                    "expression": expression,
                    "result": result,
                    "formatted_result": f"{expression} = {result}"
                },
                metadata={
                    "calculation_type": "basic_arithmetic",
                    "expression_length": len(expression)
                }
            )
            
        except ZeroDivisionError:
            return TaskResult(
                success=False,
                error="Division by zero is not allowed"
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error=f"Calculation error: {str(e)}"
            )
    
    def _extract_math_expression(self, text: str) -> Optional[str]:
        """Extract mathematical expression from text"""
        # Look for mathematical expressions
        patterns = [
            r'(\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*)',
            r'(\d+\s+(?:plus|minus|times|divided by)\s+\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                expr = match.group(1)
                # Convert word operations to symbols
                expr = expr.replace(" plus ", " + ")
                expr = expr.replace(" minus ", " - ")
                expr = expr.replace(" times ", " * ")
                expr = expr.replace(" divided by ", " / ")
                return expr
        
        return None
    
    def _clean_expression(self, expression: str) -> str:
        """Clean mathematical expression"""
        # Remove extra spaces
        cleaned = re.sub(r'\s+', '', expression)
        
        # Ensure only valid characters
        if not re.match(r'^[\d\+\-\*\/\.\(\)]+$', cleaned):
            raise ValueError("Invalid characters in expression")
        
        return cleaned
    
    def _is_safe_expression(self, expression: str) -> bool:
        """Check if expression is safe to evaluate"""
        # Only allow basic arithmetic operations
        allowed_chars = set('0123456789+-*/.() ')
        return all(c in allowed_chars for c in expression)


class WeatherTaskHandler(TaskHandler):
    """Handler for weather-related tasks"""
    
    @property
    def name(self) -> str:
        return "weather"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a weather task"""
        return task.command.startswith("weather.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a weather task"""
        try:
            command = task.command.replace("weather.", "")
            
            if command == "current":
                return await self._get_current_weather(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown weather command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Weather task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _get_current_weather(self, task: Task) -> TaskResult:
        """Get current weather (mock implementation)"""
        location = task.parameters.get("location", "your location")
        
        # Mock weather data
        mock_weather = {
            "location": location,
            "temperature": "22°C",
            "condition": "Partly cloudy",
            "humidity": "65%",
            "wind": "10 km/h",
            "description": f"The weather in {location} is partly cloudy with a temperature of 22°C."
        }
        
        return TaskResult(
            success=True,
            data=mock_weather,
            metadata={
                "source": "mock_data",
                "timestamp": datetime.now().isoformat()
            }
        )


class FileTaskHandler(TaskHandler):
    """Handler for file operations"""
    
    @property
    def name(self) -> str:
        return "file"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a file task"""
        return task.command.startswith("file.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a file operation task"""
        try:
            # Extract operation from command
            command_parts = task.command.split(".")
            if len(command_parts) < 2:
                return TaskResult(
                    success=False,
                    error="Invalid file command format"
                )
            
            operation = command_parts[1]
            
            # Check if operation requires confirmation
            if task.parameters.get("require_confirmation", True):
                return TaskResult(
                    success=False,
                    error="File operations require user confirmation in safe mode",
                    metadata={"requires_confirmation": True}
                )
            
            if operation == "list":
                return await self._list_files(task)
            elif operation == "create":
                return await self._create_file(task)
            elif operation == "delete":
                return await self._delete_file(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown file operation: {operation}"
                )
                
        except Exception as e:
            logger.error(f"File task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _list_files(self, task: Task) -> TaskResult:
        """List files in directory"""
        import os
        
        path = task.parameters.get("path", ".")
        
        try:
            files = os.listdir(path)
            return TaskResult(
                success=True,
                data={
                    "path": path,
                    "files": files,
                    "count": len(files)
                }
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error=f"Failed to list files: {str(e)}"
            )
    
    async def _create_file(self, task: Task) -> TaskResult:
        """Create a new file"""
        return TaskResult(
            success=False,
            error="File creation disabled in safe mode"
        )
    
    async def _delete_file(self, task: Task) -> TaskResult:
        """Delete a file"""
        return TaskResult(
            success=False,
            error="File deletion disabled in safe mode"
        )


class WebTaskHandler(TaskHandler):
    """Handler for web-related tasks"""
    
    @property
    def name(self) -> str:
        return "web"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a web task"""
        return task.command.startswith("web.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a web task"""
        try:
            command = task.command.replace("web.", "")
            
            if command == "search":
                return await self._search_web(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown web command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Web task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _search_web(self, task: Task) -> TaskResult:
        """Perform web search (mock implementation)"""
        query = task.parameters.get("query", "")
        
        if not query:
            return TaskResult(
                success=False,
                error="No search query provided"
            )
        
        # Mock search results
        mock_results = [
            {
                "title": f"Search result 1 for '{query}'",
                "url": f"https://example.com/result1?q={query}",
                "snippet": f"This is a mock search result for the query '{query}'. In a real implementation, this would connect to a search API."
            },
            {
                "title": f"Search result 2 for '{query}'",
                "url": f"https://example.com/result2?q={query}",
                "snippet": f"Another mock result for '{query}' with relevant information."
            }
        ]
        
        return TaskResult(
            success=True,
            data={
                "query": query,
                "results": mock_results,
                "count": len(mock_results)
            },
            metadata={
                "source": "mock_search",
                "timestamp": datetime.now().isoformat()
            }
        )


class ReminderTaskHandler(TaskHandler):
    """Handler for reminder tasks"""
    
    @property
    def name(self) -> str:
        return "reminder"
    
    def can_handle(self, task: Task) -> bool:
        """Check if this is a reminder task"""
        return task.command.startswith("reminder.")
    
    async def execute(self, task: Task) -> TaskResult:
        """Execute a reminder task"""
        try:
            command = task.command.replace("reminder.", "")
            
            if command == "create":
                return await self._create_reminder(task)
            else:
                return TaskResult(
                    success=False,
                    error=f"Unknown reminder command: {command}"
                )
                
        except Exception as e:
            logger.error(f"Reminder task execution error: {e}")
            return TaskResult(
                success=False,
                error=str(e)
            )
    
    async def _create_reminder(self, task: Task) -> TaskResult:
        """Create a new reminder"""
        message = task.parameters.get("message", "Reminder")
        reminder_time = task.parameters.get("reminder_time")
        reminder_date = task.parameters.get("reminder_date")
        
        reminder_data = {
            "message": message,
            "created_at": datetime.now().isoformat(),
            "reminder_time": reminder_time,
            "reminder_date": reminder_date,
            "status": "active"
        }
        
        return TaskResult(
            success=True,
            data=reminder_data,
            metadata={
                "reminder_type": "basic",
                "has_time": bool(reminder_time),
                "has_date": bool(reminder_date)
            }
        )
