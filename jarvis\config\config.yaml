# Basic JARVIS Configuration for Testing
# This is a minimal configuration to get started

ai:
  provider: "openai"
  openai:
    api_key: "********************************************************************************************************************************************************************"
    model: "gpt-3.5-turbo"
    temperature: 0.7
    max_tokens: 2000

voice:
  # Speech recognition settings
  recognition:
    provider: "whisper" # whisper, google, azure
    language: "en-US"
    sample_rate: 16000
    chunk_size: 1024
    whisper_model: "base" # tiny, base, small, medium, large
    azure_region: "eastus"

  # Text-to-speech settings
  tts:
    provider: "pyttsx3" # elevenlabs, azure, gtts, pyttsx3
    voice_id: "default"
    language: "en-US"
    speed: 1.0
    pitch: 0.0
    azure_region: "eastus"

  # Voice activation settings
  activation:
    enabled: true
    timeout: 10.0

  # Wake words for voice activation
  wake_words:
    - "jarvis"
    - "hey jarvis"
    - "computer"
    - "assistant"

  # Voice personality settings
  personality: "professional" # professional, friendly, enthusiastic
  response_style: "concise" # concise, detailed, casual

system:
  file_operations:
    enabled: true
    safe_mode: true
  app_control:
    enabled: true
    safe_mode: true
  system_commands:
    enabled: false
    safe_mode: true

web:
  browser:
    provider: "selenium"
    headless: false
    default_browser: "chrome"
  search:
    provider: "google"
    results_limit: 10

memory:
  conversation:
    max_history: 100
    persist: true
  personal:
    enabled: true
    encryption: false

interface:
  cli:
    prompt: "JARVIS> "
    colors: true
    auto_complete: true

security:
  permissions:
    require_confirmation: true
    log_all_actions: true

logging:
  level: "INFO"
  file_path: "logs/jarvis.log"
  max_file_size: "10MB"
  backup_count: 5

development:
  debug_mode: false
  test_mode: false
