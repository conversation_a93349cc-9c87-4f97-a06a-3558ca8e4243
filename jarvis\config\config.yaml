# Basic JARVIS Configuration for Testing
# This is a minimal configuration to get started

ai:
  provider: "openai"
  openai:
    api_key: ""  # Add your OpenAI API key here
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 2000

voice:
  recognition:
    provider: "whisper"
    language: "en-US"
  synthesis:
    provider: "pyttsx3"
    rate: 200
    volume: 0.9

system:
  file_operations:
    enabled: true
    safe_mode: true
  app_control:
    enabled: true
    safe_mode: true
  system_commands:
    enabled: false
    safe_mode: true

web:
  browser:
    provider: "selenium"
    headless: false
    default_browser: "chrome"
  search:
    provider: "google"
    results_limit: 10

memory:
  conversation:
    max_history: 100
    persist: true
  personal:
    enabled: true
    encryption: false

interface:
  cli:
    prompt: "JARVIS> "
    colors: true
    auto_complete: true

security:
  permissions:
    require_confirmation: true
    log_all_actions: true

logging:
  level: "INFO"
  file_path: "logs/jarvis.log"
  max_file_size: "10MB"
  backup_count: 5

development:
  debug_mode: false
  test_mode: false
