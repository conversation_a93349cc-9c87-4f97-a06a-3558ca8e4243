"""
Task Executor for JARVIS AI Assistant
"""

import asyncio
import time
from typing import Dict, List, Optional, Type
import logging
from concurrent.futures import ThreadPoolExecutor

from .base import Task, TaskResult, TaskStatus, TaskHandler, SystemTaskHandler

logger = logging.getLogger(__name__)


class TaskExecutor:
    """Main task executor for JARVIS"""
    
    def __init__(self, max_workers: int = 4):
        """
        Initialize the task executor
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.handlers: Dict[str, TaskHandler] = {}
        self.task_queue: List[Task] = []
        self.running_tasks: Dict[str, Task] = {}
        self.completed_tasks: List[Task] = []
        self.is_running = False
        
        # Register default handlers
        self._register_default_handlers()
        
        logger.info(f"Task executor initialized with {max_workers} workers")
    
    def _register_default_handlers(self):
        """Register default task handlers"""
        self.register_handler(SystemTaskHandler())
    
    def register_handler(self, handler: TaskHandler):
        """
        Register a task handler
        
        Args:
            handler: Task handler to register
        """
        self.handlers[handler.name] = handler
        logger.info(f"Registered task handler: {handler.name}")
    
    def unregister_handler(self, name: str) -> bool:
        """
        Unregister a task handler
        
        Args:
            name: Name of the handler to unregister
            
        Returns:
            True if handler was removed, False if not found
        """
        if name in self.handlers:
            del self.handlers[name]
            logger.info(f"Unregistered task handler: {name}")
            return True
        return False
    
    def get_handler_for_task(self, task: Task) -> Optional[TaskHandler]:
        """
        Get the appropriate handler for a task
        
        Args:
            task: Task to find handler for
            
        Returns:
            Task handler or None if no handler found
        """
        for handler in self.handlers.values():
            if handler.can_handle(task):
                return handler
        return None
    
    async def submit_task(self, task: Task) -> str:
        """
        Submit a task for execution
        
        Args:
            task: Task to submit
            
        Returns:
            Task ID
        """
        # Check if we have a handler for this task
        handler = self.get_handler_for_task(task)
        if not handler:
            task.complete(TaskResult(
                success=False,
                error=f"No handler found for task: {task.command}"
            ))
            return task.id
        
        # Add to queue
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda t: t.priority, reverse=True)
        
        logger.info(f"Task submitted: {task.id} - {task.name}")
        return task.id
    
    async def execute_task(self, task: Task) -> TaskResult:
        """
        Execute a single task
        
        Args:
            task: Task to execute
            
        Returns:
            Task result
        """
        handler = self.get_handler_for_task(task)
        if not handler:
            return TaskResult(
                success=False,
                error=f"No handler found for task: {task.command}"
            )
        
        try:
            # Validate task
            if not await handler.validate_task(task):
                return TaskResult(
                    success=False,
                    error="Task validation failed"
                )
            
            # Prepare task
            if not await handler.prepare_task(task):
                return TaskResult(
                    success=False,
                    error="Task preparation failed"
                )
            
            # Mark task as started
            task.start()
            self.running_tasks[task.id] = task
            
            start_time = time.time()
            
            # Execute task with timeout
            if task.timeout:
                result = await asyncio.wait_for(
                    handler.execute(task),
                    timeout=task.timeout
                )
            else:
                result = await handler.execute(task)
            
            # Calculate execution time
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            # Cleanup
            await handler.cleanup_task(task, result)
            
            # Complete task
            task.complete(result)
            
            # Move from running to completed
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            self.completed_tasks.append(task)
            
            logger.info(f"Task completed: {task.id} - Success: {result.success}")
            return result
            
        except asyncio.TimeoutError:
            result = TaskResult(
                success=False,
                error=f"Task timed out after {task.timeout} seconds"
            )
            task.complete(result)
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            self.completed_tasks.append(task)
            logger.warning(f"Task timed out: {task.id}")
            return result
            
        except Exception as e:
            result = TaskResult(
                success=False,
                error=str(e)
            )
            task.complete(result)
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            self.completed_tasks.append(task)
            logger.error(f"Task execution error: {task.id} - {e}")
            return result
    
    async def process_queue(self):
        """Process the task queue"""
        while self.is_running:
            if not self.task_queue:
                await asyncio.sleep(0.1)
                continue
            
            # Get next task
            task = self.task_queue.pop(0)
            
            # Execute task
            await self.execute_task(task)
    
    async def start(self):
        """Start the task executor"""
        if self.is_running:
            logger.warning("Task executor is already running")
            return
        
        self.is_running = True
        logger.info("Task executor started")
        
        # Start processing queue
        await self.process_queue()
    
    def stop(self):
        """Stop the task executor"""
        self.is_running = False
        
        # Cancel running tasks
        for task in self.running_tasks.values():
            task.cancel()
        
        self.running_tasks.clear()
        self.thread_pool.shutdown(wait=True)
        
        logger.info("Task executor stopped")
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        Get the status of a task
        
        Args:
            task_id: Task ID
            
        Returns:
            Task status or None if task not found
        """
        # Check running tasks
        if task_id in self.running_tasks:
            return self.running_tasks[task_id].status
        
        # Check completed tasks
        for task in self.completed_tasks:
            if task.id == task_id:
                return task.status
        
        # Check queue
        for task in self.task_queue:
            if task.id == task_id:
                return task.status
        
        return None
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        Get a task by ID
        
        Args:
            task_id: Task ID
            
        Returns:
            Task or None if not found
        """
        # Check running tasks
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        # Check completed tasks
        for task in self.completed_tasks:
            if task.id == task_id:
                return task
        
        # Check queue
        for task in self.task_queue:
            if task.id == task_id:
                return task
        
        return None
    
    def get_stats(self) -> Dict[str, int]:
        """Get task executor statistics"""
        return {
            "queued": len(self.task_queue),
            "running": len(self.running_tasks),
            "completed": len(self.completed_tasks),
            "handlers": len(self.handlers)
        }
    
    def clear_completed_tasks(self):
        """Clear completed tasks"""
        self.completed_tasks.clear()
        logger.info("Cleared completed tasks")
