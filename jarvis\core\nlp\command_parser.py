"""
Command Parser for JARVIS AI Assistant
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CommandTemplate:
    """Template for command parsing"""
    intent: str
    command: str
    parameter_mapping: Dict[str, str]
    required_entities: List[str] = None
    optional_entities: List[str] = None
    
    def __post_init__(self):
        if self.required_entities is None:
            self.required_entities = []
        if self.optional_entities is None:
            self.optional_entities = []


class CommandParser:
    """Parses natural language into executable commands"""
    
    def __init__(self):
        """Initialize the command parser"""
        self.command_templates = self._load_command_templates()
        self.parsing_stats = {
            "total_parses": 0,
            "successful_parses": 0,
            "command_counts": {}
        }
        
        logger.info("Command parser initialized")
    
    def _load_command_templates(self) -> List[CommandTemplate]:
        """Load command templates"""
        return [
            # Time and date commands
            CommandTemplate(
                intent="get_time",
                command="system.time",
                parameter_mapping={}
            ),
            
            CommandTemplate(
                intent="get_date",
                command="system.date",
                parameter_mapping={}
            ),
            
            # System information commands
            CommandTemplate(
                intent="system_info",
                command="system.info",
                parameter_mapping={}
            ),
            
            # File operation commands
            CommandTemplate(
                intent="file_operation",
                command="file.{operation_type}",
                parameter_mapping={
                    "file_path": "path",
                    "operation": "operation_type"
                },
                required_entities=["operation"],
                optional_entities=["file_path"]
            ),
            
            # Calculation commands
            CommandTemplate(
                intent="calculation",
                command="math.calculate",
                parameter_mapping={
                    "number": "operands",
                    "math_operations": "operations"
                },
                required_entities=["number"]
            ),
            
            # Search commands
            CommandTemplate(
                intent="search",
                command="web.search",
                parameter_mapping={
                    "search_query": "query",
                    "url": "target_url"
                },
                required_entities=["search_query"]
            ),
            
            # Weather commands
            CommandTemplate(
                intent="get_weather",
                command="weather.current",
                parameter_mapping={
                    "location": "location"
                },
                optional_entities=["location"]
            ),
            
            # Email commands
            CommandTemplate(
                intent="send_email",
                command="email.send",
                parameter_mapping={
                    "email": "recipient",
                    "person": "recipient_name"
                },
                optional_entities=["email", "person"]
            ),
            
            # Reminder commands
            CommandTemplate(
                intent="set_reminder",
                command="reminder.create",
                parameter_mapping={
                    "time": "reminder_time",
                    "date": "reminder_date"
                },
                optional_entities=["time", "date"]
            ),
            
            # System control commands
            CommandTemplate(
                intent="system_control",
                command="system.{operation}",
                parameter_mapping={
                    "operation": "operation"
                },
                required_entities=["operation"]
            ),
            
            # Application control commands
            CommandTemplate(
                intent="app_control",
                command="app.{operation}",
                parameter_mapping={
                    "operation": "action",
                    "file_path": "app_name"
                },
                required_entities=["operation"]
            ),
            
            # Media control commands
            CommandTemplate(
                intent="media_control",
                command="media.{operation}",
                parameter_mapping={
                    "operation": "action"
                },
                required_entities=["operation"]
            ),
            
            # Note-taking commands
            CommandTemplate(
                intent="take_note",
                command="notes.create",
                parameter_mapping={},
                optional_entities=["file_path"]
            ),

            # System monitoring commands
            CommandTemplate(
                intent="system_monitor",
                command="monitor.{monitor_type}",
                parameter_mapping={
                    "operation": "monitor_type"
                },
                optional_entities=["operation"]
            ),

            # Application management commands
            CommandTemplate(
                intent="app_management",
                command="app.{app_operation}",
                parameter_mapping={
                    "operation": "app_operation",
                    "file_path": "app_name"
                },
                required_entities=["operation"]
            ),

            # Advanced file operations
            CommandTemplate(
                intent="advanced_file_ops",
                command="file.{file_operation}",
                parameter_mapping={
                    "operation": "file_operation",
                    "file_path": "path"
                },
                required_entities=["operation"]
            ),

            # Web search commands
            CommandTemplate(
                intent="web_search",
                command="web.search",
                parameter_mapping={
                    "query": "query",
                    "search_type": "search_type"
                },
                optional_entities=["search_type"]
            ),

            # Web browsing commands
            CommandTemplate(
                intent="web_browse",
                command="web.{web_operation}",
                parameter_mapping={
                    "operation": "web_operation",
                    "url": "url",
                    "file_path": "filename"
                },
                optional_entities=["operation", "url", "filename"]
            ),

            # Content extraction commands
            CommandTemplate(
                intent="content_extraction",
                command="web.{extract_operation}",
                parameter_mapping={
                    "operation": "extract_operation",
                    "url": "url",
                    "content_type": "content_type"
                },
                optional_entities=["operation", "content_type"]
            ),

            # Voice control commands
            CommandTemplate(
                intent="voice_control",
                command="voice.{voice_operation}",
                parameter_mapping={
                    "operation": "voice_operation",
                    "text": "text",
                    "duration": "duration"
                },
                optional_entities=["operation", "text", "duration"]
            )
        ]
    
    def parse(
        self, 
        text: str, 
        intent: str, 
        entities: Dict[str, Any]
    ) -> Tuple[Optional[str], Dict[str, Any]]:
        """
        Parse text into command and parameters
        
        Args:
            text: Original text
            intent: Recognized intent
            entities: Extracted entities
            
        Returns:
            Tuple of (command, parameters)
        """
        self.parsing_stats["total_parses"] += 1
        
        # Find matching command template
        template = self._find_matching_template(intent, entities)
        if not template:
            logger.debug(f"No command template found for intent: {intent}")
            return None, {}
        
        # Build command
        command = self._build_command(template, entities, text)
        if not command:
            logger.debug(f"Failed to build command for intent: {intent}")
            return None, {}
        
        # Build parameters
        parameters = self._build_parameters(template, entities, text)
        
        # Update stats
        self.parsing_stats["successful_parses"] += 1
        self.parsing_stats["command_counts"][command] = \
            self.parsing_stats["command_counts"].get(command, 0) + 1
        
        logger.debug(f"Parsed command: {command} with parameters: {list(parameters.keys())}")
        return command, parameters
    
    def _find_matching_template(self, intent: str, entities: Dict[str, Any]) -> Optional[CommandTemplate]:
        """Find matching command template"""
        for template in self.command_templates:
            if template.intent == intent:
                # Check if required entities are present
                if all(entity in entities for entity in template.required_entities):
                    return template
                # If no required entities, return the template
                elif not template.required_entities:
                    return template
        
        return None
    
    def _build_command(self, template: CommandTemplate, entities: Dict[str, Any], text: str) -> Optional[str]:
        """Build command from template"""
        command = template.command
        
        # Replace placeholders in command
        placeholders = re.findall(r'\{(\w+)\}', command)
        for placeholder in placeholders:
            replacement = self._get_replacement_value(placeholder, entities, text)
            if replacement:
                command = command.replace(f"{{{placeholder}}}", replacement)
            else:
                # If we can't replace a required placeholder, return None
                return None
        
        return command
    
    def _get_replacement_value(self, placeholder: str, entities: Dict[str, Any], text: str) -> Optional[str]:
        """Get replacement value for command placeholder"""
        # Direct entity mapping
        if placeholder in entities and entities[placeholder]:
            entity_value = entities[placeholder][0]["normalized"]
            if isinstance(entity_value, str):
                return entity_value.lower()
            return str(entity_value)
        
        # Special mappings
        if placeholder == "operation_type":
            if "operation" in entities:
                op = entities["operation"][0]["normalized"].lower()
                # Map operation words to standard operations
                operation_map = {
                    "create": "create", "make": "create", "build": "create", "generate": "create",
                    "delete": "delete", "remove": "delete", "erase": "delete",
                    "update": "update", "modify": "update", "change": "update", "edit": "update",
                    "copy": "copy", "duplicate": "copy", "clone": "copy",
                    "move": "move", "transfer": "move", "relocate": "move",
                    "open": "open", "launch": "open", "start": "open", "run": "open",
                    "close": "close", "quit": "close", "exit": "close", "stop": "close"
                }
                return operation_map.get(op, op)
        
        elif placeholder == "operation":
            # Extract operation from text for system control
            system_ops = {
                "shutdown": "shutdown", "power off": "shutdown", "turn off": "shutdown",
                "restart": "restart", "reboot": "restart",
                "sleep": "sleep", "hibernate": "hibernate",
                "lock": "lock"
            }
            
            text_lower = text.lower()
            for phrase, operation in system_ops.items():
                if phrase in text_lower:
                    return operation
        
        return None
    
    def _build_parameters(self, template: CommandTemplate, entities: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Build parameters from entities"""
        parameters = {}
        
        # Map entities to parameters
        for entity_type, param_name in template.parameter_mapping.items():
            if entity_type in entities:
                entity_list = entities[entity_type]
                if len(entity_list) == 1:
                    parameters[param_name] = entity_list[0]["normalized"]
                else:
                    # Multiple entities of same type
                    parameters[param_name] = [e["normalized"] for e in entity_list]
        
        # Add original text for context
        parameters["original_text"] = text
        
        # Add intent-specific parameters
        parameters.update(self._get_intent_specific_parameters(template.intent, entities, text))
        
        return parameters
    
    def _get_intent_specific_parameters(self, intent: str, entities: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Get intent-specific parameters"""
        params = {}
        
        if intent == "calculation":
            # Extract the full mathematical expression
            math_expr = self._extract_math_expression(text)
            if math_expr:
                params["expression"] = math_expr
        
        elif intent == "search":
            # Extract search query if not already found
            if "search_query" not in entities:
                query = self._extract_search_query(text)
                if query:
                    params["query"] = query
        
        elif intent == "file_operation":
            # Add safety parameters
            params["safe_mode"] = True
            params["require_confirmation"] = True
        
        elif intent == "system_control":
            # Add safety parameters for system operations
            params["require_confirmation"] = True
            params["timeout"] = 30  # 30 second timeout
        
        elif intent == "send_email":
            # Extract email content
            content = self._extract_email_content(text)
            if content:
                params["content"] = content
        
        elif intent == "set_reminder":
            # Extract reminder message
            message = self._extract_reminder_message(text)
            if message:
                params["message"] = message
        
        return params
    
    def _extract_math_expression(self, text: str) -> Optional[str]:
        """Extract mathematical expression from text"""
        # Look for mathematical expressions
        math_pattern = r'(\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*)'
        match = re.search(math_pattern, text)
        if match:
            return match.group(1)
        
        # Look for word-based math
        word_math = re.search(r'(\d+\s+(?:plus|minus|times|divided by)\s+\d+)', text, re.IGNORECASE)
        if word_math:
            return word_math.group(1)
        
        return None
    
    def _extract_search_query(self, text: str) -> Optional[str]:
        """Extract search query from text"""
        # Remove search command words
        search_words = ["search", "find", "look for", "google", "bing"]
        query = text.lower()
        
        for word in search_words:
            query = query.replace(word, "").strip()
        
        # Remove common prepositions
        prepositions = ["for", "about", "on", "in", "at", "by", "with"]
        words = query.split()
        filtered_words = [w for w in words if w not in prepositions]
        
        return " ".join(filtered_words) if filtered_words else None
    
    def _extract_email_content(self, text: str) -> Optional[str]:
        """Extract email content from text"""
        # Look for content after email command words
        email_words = ["send email", "email", "compose", "write email"]
        content = text.lower()
        
        for word in email_words:
            if word in content:
                # Get text after the email command
                parts = content.split(word, 1)
                if len(parts) > 1:
                    return parts[1].strip()
        
        return None
    
    def _extract_reminder_message(self, text: str) -> Optional[str]:
        """Extract reminder message from text"""
        # Look for content after reminder command words
        reminder_words = ["remind me", "reminder", "don't forget"]
        content = text.lower()
        
        for word in reminder_words:
            if word in content:
                # Get text after the reminder command
                parts = content.split(word, 1)
                if len(parts) > 1:
                    message = parts[1].strip()
                    # Remove time/date information
                    message = re.sub(r'\b(?:at|on|in)\s+\d+[:\d\s]*(?:am|pm)?\b', '', message, flags=re.IGNORECASE)
                    message = re.sub(r'\b(?:today|tomorrow|yesterday|next week|next month)\b', '', message, flags=re.IGNORECASE)
                    return message.strip()
        
        return None
    
    def add_command_template(self, template: CommandTemplate):
        """Add a custom command template"""
        self.command_templates.append(template)
        logger.info(f"Added command template for intent: {template.intent}")
    
    def get_supported_commands(self) -> List[str]:
        """Get list of supported commands"""
        commands = []
        for template in self.command_templates:
            # Replace placeholders with generic names
            command = template.command
            placeholders = re.findall(r'\{(\w+)\}', command)
            for placeholder in placeholders:
                command = command.replace(f"{{{placeholder}}}", f"<{placeholder}>")
            commands.append(command)
        
        return commands
    
    def get_stats(self) -> Dict[str, Any]:
        """Get parsing statistics"""
        success_rate = 0.0
        if self.parsing_stats["total_parses"] > 0:
            success_rate = self.parsing_stats["successful_parses"] / self.parsing_stats["total_parses"]
        
        return {
            "total_parses": self.parsing_stats["total_parses"],
            "successful_parses": self.parsing_stats["successful_parses"],
            "success_rate": success_rate,
            "supported_commands": len(self.command_templates),
            "command_distribution": self.parsing_stats["command_counts"]
        }
