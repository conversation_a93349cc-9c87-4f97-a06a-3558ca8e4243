"""
Intent Recognition for JARVIS AI Assistant
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class IntentPattern:
    """Pattern for intent recognition"""
    intent: str
    patterns: List[str]
    keywords: List[str]
    confidence_boost: float = 0.0
    context_dependent: bool = False


class IntentRecognizer:
    """Recognizes user intents from natural language"""
    
    def __init__(self):
        """Initialize the intent recognizer"""
        self.intent_patterns = self._load_intent_patterns()
        self.recognition_stats = {
            "total_recognitions": 0,
            "successful_recognitions": 0,
            "intent_counts": {}
        }
        
        logger.info("Intent recognizer initialized")
    
    def _load_intent_patterns(self) -> List[IntentPattern]:
        """Load intent recognition patterns"""
        patterns = [
            # Greeting intents
            IntentPattern(
                intent="greeting",
                patterns=[
                    r"\b(hello|hi|hey|greetings|good morning|good afternoon|good evening)\b",
                    r"\b(how are you|what's up|how's it going)\b"
                ],
                keywords=["hello", "hi", "hey", "greetings", "morning", "afternoon", "evening"]
            ),
            
            # Time and date intents
            IntentPattern(
                intent="get_time",
                patterns=[
                    r"\b(what time|current time|time is|tell me the time)\b",
                    r"\b(what's the time|time now|current time)\b"
                ],
                keywords=["time", "clock", "hour", "minute"]
            ),
            
            IntentPattern(
                intent="get_date",
                patterns=[
                    r"\b(what date|current date|today's date|what day)\b",
                    r"\b(date today|today is|what's today)\b"
                ],
                keywords=["date", "today", "day", "calendar"]
            ),
            
            # Calculation intents
            IntentPattern(
                intent="calculation",
                patterns=[
                    r"\b(calculate|compute|what is|solve)\b.*[\+\-\*\/\=]",
                    r"\d+\s*[\+\-\*\/]\s*\d+",
                    r"\b(plus|minus|times|divided by|multiply|add|subtract)\b"
                ],
                keywords=["calculate", "compute", "math", "plus", "minus", "times", "divided", "equals"]
            ),
            
            # Weather intents
            IntentPattern(
                intent="get_weather",
                patterns=[
                    r"\b(weather|temperature|forecast|rain|sunny|cloudy)\b",
                    r"\b(how's the weather|weather like|weather forecast)\b"
                ],
                keywords=["weather", "temperature", "forecast", "rain", "sunny", "cloudy", "storm"]
            ),
            
            # File operation intents
            IntentPattern(
                intent="file_operation",
                patterns=[
                    r"\b(open|create|delete|move|copy|rename|find)\b.*\b(file|folder|directory)\b",
                    r"\b(list|show|display)\b.*\b(files|folders|directory)\b"
                ],
                keywords=["file", "folder", "directory", "open", "create", "delete", "move", "copy"],
                confidence_boost=0.1
            ),
            
            # Search intents
            IntentPattern(
                intent="search",
                patterns=[
                    r"\b(search|find|look for|google|bing)\b",
                    r"\b(search for|look up|find information about)\b"
                ],
                keywords=["search", "find", "look", "google", "information"]
            ),
            
            # System intents
            IntentPattern(
                intent="system_info",
                patterns=[
                    r"\b(system info|system status|computer info|hardware info)\b",
                    r"\b(cpu|memory|disk|performance|specs)\b"
                ],
                keywords=["system", "computer", "hardware", "cpu", "memory", "disk", "performance"]
            ),
            
            # Email intents
            IntentPattern(
                intent="send_email",
                patterns=[
                    r"\b(send email|email|compose|write email)\b",
                    r"\b(send message|send mail)\b"
                ],
                keywords=["email", "send", "compose", "message", "mail"],
                confidence_boost=0.2
            ),
            
            # Reminder intents
            IntentPattern(
                intent="set_reminder",
                patterns=[
                    r"\b(remind me|set reminder|reminder|don't forget)\b",
                    r"\b(schedule|appointment|meeting)\b"
                ],
                keywords=["remind", "reminder", "schedule", "appointment", "meeting", "calendar"]
            ),
            
            # Help intents
            IntentPattern(
                intent="help",
                patterns=[
                    r"\b(help|assist|support|how to|what can you do)\b",
                    r"\b(commands|features|capabilities)\b"
                ],
                keywords=["help", "assist", "support", "commands", "features", "capabilities"]
            ),
            
            # Control intents
            IntentPattern(
                intent="system_control",
                patterns=[
                    r"\b(shutdown|restart|sleep|hibernate|lock)\b",
                    r"\b(turn off|power off|reboot)\b"
                ],
                keywords=["shutdown", "restart", "sleep", "hibernate", "lock", "power"],
                confidence_boost=0.3
            ),
            
            # Application control
            IntentPattern(
                intent="app_control",
                patterns=[
                    r"\b(open|launch|start|close|quit)\b.*\b(application|app|program)\b",
                    r"\b(run|execute)\b"
                ],
                keywords=["open", "launch", "start", "close", "quit", "application", "program", "run"]
            ),
            
            # Music/media intents
            IntentPattern(
                intent="media_control",
                patterns=[
                    r"\b(play|pause|stop|next|previous)\b.*\b(music|song|video|media)\b",
                    r"\b(volume|mute|unmute)\b"
                ],
                keywords=["play", "pause", "stop", "music", "song", "video", "volume", "mute"]
            ),
            
            # Note-taking intents
            IntentPattern(
                intent="take_note",
                patterns=[
                    r"\b(take note|note|write down|remember this)\b",
                    r"\b(save|record|log)\b"
                ],
                keywords=["note", "write", "save", "record", "remember", "log"]
            ),

            # System monitoring intents
            IntentPattern(
                intent="system_monitor",
                patterns=[
                    r"\b(system usage|resource usage|performance|monitor)\b",
                    r"\b(cpu usage|memory usage|disk usage|processes)\b",
                    r"\b(system health|check system|system status)\b"
                ],
                keywords=["usage", "performance", "monitor", "health", "resources", "processes"]
            ),

            # Application management intents
            IntentPattern(
                intent="app_management",
                patterns=[
                    r"\b(launch|start|open|run)\b.*\b(application|app|program)\b",
                    r"\b(close|quit|kill|stop)\b.*\b(application|app|program)\b",
                    r"\b(list|show)\b.*\b(running|applications|apps|processes)\b"
                ],
                keywords=["launch", "start", "open", "close", "quit", "application", "program", "running"]
            ),

            # Advanced file operations
            IntentPattern(
                intent="advanced_file_ops",
                patterns=[
                    r"\b(search|find)\b.*\b(files|documents)\b",
                    r"\b(copy|move|rename|backup)\b.*\b(file|folder)\b",
                    r"\b(file info|file details|properties)\b"
                ],
                keywords=["search", "find", "copy", "move", "rename", "backup", "properties", "details"]
            ),

            # Web browsing and search intents
            IntentPattern(
                intent="web_search",
                patterns=[
                    r"\b(search|google|find|look up)\b.*\b(web|internet|online)\b",
                    r"\b(search for|look for|find)\b",
                    r"\b(what is|who is|where is|when is|how to)\b"
                ],
                keywords=["search", "google", "find", "web", "internet", "online", "lookup"]
            ),

            # Web browsing intents
            IntentPattern(
                intent="web_browse",
                patterns=[
                    r"\b(browse|visit|go to|open)\b.*\b(website|url|page)\b",
                    r"\b(download|get)\b.*\b(file|image|document)\b",
                    r"\b(screenshot|capture)\b.*\b(page|website)\b"
                ],
                keywords=["browse", "visit", "website", "url", "download", "screenshot", "capture"]
            ),

            # Content extraction intents
            IntentPattern(
                intent="content_extraction",
                patterns=[
                    r"\b(extract|scrape|get)\b.*\b(content|text|data)\b",
                    r"\b(read|summarize)\b.*\b(article|page|website)\b",
                    r"\b(get links|find links|extract links)\b"
                ],
                keywords=["extract", "scrape", "content", "text", "article", "summarize", "links"]
            )
        ]
        
        return patterns
    
    def recognize(self, text: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """
        Recognize intent from text
        
        Args:
            text: Input text
            context: Optional context information
            
        Returns:
            Tuple of (intent, confidence_score)
        """
        self.recognition_stats["total_recognitions"] += 1
        
        text_lower = text.lower()
        intent_scores = {}
        
        # Score each intent pattern
        for pattern in self.intent_patterns:
            score = self._calculate_intent_score(text_lower, pattern, context)
            if score > 0:
                intent_scores[pattern.intent] = max(intent_scores.get(pattern.intent, 0), score)
        
        # Find best matching intent
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[best_intent]
            
            # Update stats
            self.recognition_stats["successful_recognitions"] += 1
            self.recognition_stats["intent_counts"][best_intent] = \
                self.recognition_stats["intent_counts"].get(best_intent, 0) + 1
            
            logger.debug(f"Intent recognized: {best_intent} (confidence: {confidence:.2f})")
            return best_intent, confidence
        
        # Fallback to unknown intent
        logger.debug("No intent recognized, using 'unknown'")
        return "unknown", 0.0
    
    def _calculate_intent_score(
        self, 
        text: str, 
        pattern: IntentPattern, 
        context: Optional[Dict[str, Any]]
    ) -> float:
        """
        Calculate score for an intent pattern
        
        Args:
            text: Input text
            pattern: Intent pattern to match
            context: Context information
            
        Returns:
            Score between 0 and 1
        """
        score = 0.0
        
        # Pattern matching score
        pattern_matches = 0
        for regex_pattern in pattern.patterns:
            if re.search(regex_pattern, text, re.IGNORECASE):
                pattern_matches += 1
        
        if pattern_matches > 0:
            score += 0.6 * (pattern_matches / len(pattern.patterns))
        
        # Keyword matching score
        keyword_matches = 0
        words = text.split()
        for keyword in pattern.keywords:
            if keyword in text:
                keyword_matches += 1
        
        if keyword_matches > 0:
            score += 0.3 * (keyword_matches / len(pattern.keywords))
        
        # Context boost
        if context and pattern.context_dependent:
            if context.get("previous_intent") == pattern.intent:
                score += 0.1
        
        # Confidence boost
        score += pattern.confidence_boost
        
        # Normalize score to [0, 1]
        return min(score, 1.0)
    
    def add_custom_intent(
        self, 
        intent: str, 
        patterns: List[str], 
        keywords: List[str],
        confidence_boost: float = 0.0
    ):
        """
        Add a custom intent pattern
        
        Args:
            intent: Intent name
            patterns: List of regex patterns
            keywords: List of keywords
            confidence_boost: Additional confidence boost
        """
        custom_pattern = IntentPattern(
            intent=intent,
            patterns=patterns,
            keywords=keywords,
            confidence_boost=confidence_boost
        )
        
        self.intent_patterns.append(custom_pattern)
        logger.info(f"Added custom intent: {intent}")
    
    def get_supported_intents(self) -> List[str]:
        """Get list of supported intents"""
        return list(set(pattern.intent for pattern in self.intent_patterns))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get recognition statistics"""
        success_rate = 0.0
        if self.recognition_stats["total_recognitions"] > 0:
            success_rate = (
                self.recognition_stats["successful_recognitions"] / 
                self.recognition_stats["total_recognitions"]
            )
        
        return {
            "total_recognitions": self.recognition_stats["total_recognitions"],
            "successful_recognitions": self.recognition_stats["successful_recognitions"],
            "success_rate": success_rate,
            "supported_intents": len(self.get_supported_intents()),
            "intent_distribution": self.recognition_stats["intent_counts"]
        }
