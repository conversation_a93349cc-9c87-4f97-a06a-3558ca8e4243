"""
Vector Memory Storage for JARVIS AI Assistant
"""

import json
import logging
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import hashlib
from datetime import datetime

logger = logging.getLogger(__name__)


class VectorStorage:
    """Vector-based memory storage for semantic search and context retrieval"""
    
    def __init__(self, data_dir: Path, collection_name: str = "jarvis_memory"):
        """Initialize vector storage"""
        self.data_dir = data_dir
        self.collection_name = collection_name
        self.vector_db = None
        self.embedding_model = None
        self.fallback_storage = {}  # Fallback when vector DB is not available
        
        self._initialize_vector_db()
        self._initialize_embedding_model()
        
        logger.info("Vector storage initialized")
    
    def _initialize_vector_db(self):
        """Initialize vector database (ChromaDB)"""
        try:
            import chromadb
            from chromadb.config import Settings
            
            # Create persistent client
            persist_directory = str(self.data_dir / "vector_db")
            os.makedirs(persist_directory, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=persist_directory,
                settings=Settings(anonymized_telemetry=False)
            )
            
            # Get or create collection
            try:
                self.vector_db = self.client.get_collection(name=self.collection_name)
                logger.info(f"Loaded existing vector collection: {self.collection_name}")
            except:
                self.vector_db = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "JARVIS semantic memory storage"}
                )
                logger.info(f"Created new vector collection: {self.collection_name}")
                
        except ImportError:
            logger.warning("ChromaDB not available, using fallback storage")
            self.vector_db = None
        except Exception as e:
            logger.error(f"Failed to initialize vector database: {e}")
            self.vector_db = None
    
    def _initialize_embedding_model(self):
        """Initialize embedding model for text vectorization"""
        try:
            # Try to use sentence-transformers for local embeddings
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Initialized local embedding model")
        except ImportError:
            logger.warning("sentence-transformers not available, using simple embeddings")
            self.embedding_model = None
    
    def add_memory(
        self, 
        content: str, 
        memory_type: str = "conversation",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a memory to vector storage
        
        Args:
            content: Text content to store
            memory_type: Type of memory (conversation, fact, preference, etc.)
            metadata: Additional metadata
            
        Returns:
            Memory ID
        """
        # Generate unique ID
        memory_id = self._generate_memory_id(content)
        
        # Prepare metadata
        if metadata is None:
            metadata = {}
        
        metadata.update({
            "memory_type": memory_type,
            "timestamp": datetime.now().isoformat(),
            "content_length": len(content)
        })
        
        if self.vector_db:
            try:
                # Add to vector database
                self.vector_db.add(
                    documents=[content],
                    metadatas=[metadata],
                    ids=[memory_id]
                )
                logger.debug(f"Added memory to vector DB: {memory_id}")
            except Exception as e:
                logger.error(f"Failed to add memory to vector DB: {e}")
                # Fallback to simple storage
                self._add_to_fallback(memory_id, content, metadata)
        else:
            # Use fallback storage
            self._add_to_fallback(memory_id, content, metadata)
        
        return memory_id
    
    def search_memories(
        self, 
        query: str, 
        memory_type: Optional[str] = None,
        limit: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant memories
        
        Args:
            query: Search query
            memory_type: Filter by memory type
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of relevant memories
        """
        if self.vector_db:
            try:
                # Prepare where clause for filtering
                where_clause = {}
                if memory_type:
                    where_clause["memory_type"] = memory_type
                
                # Search vector database
                results = self.vector_db.query(
                    query_texts=[query],
                    n_results=limit,
                    where=where_clause if where_clause else None
                )
                
                # Format results
                memories = []
                if results['documents'] and results['documents'][0]:
                    for i, doc in enumerate(results['documents'][0]):
                        memory = {
                            "id": results['ids'][0][i],
                            "content": doc,
                            "metadata": results['metadatas'][0][i],
                            "similarity": 1.0 - results['distances'][0][i] if results['distances'] else 1.0
                        }
                        
                        # Filter by similarity threshold
                        if memory["similarity"] >= similarity_threshold:
                            memories.append(memory)
                
                logger.debug(f"Found {len(memories)} relevant memories")
                return memories
                
            except Exception as e:
                logger.error(f"Vector search failed: {e}")
                return self._search_fallback(query, memory_type, limit)
        else:
            return self._search_fallback(query, memory_type, limit)
    
    def get_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific memory by ID"""
        if self.vector_db:
            try:
                results = self.vector_db.get(ids=[memory_id])
                if results['documents']:
                    return {
                        "id": memory_id,
                        "content": results['documents'][0],
                        "metadata": results['metadatas'][0]
                    }
            except Exception as e:
                logger.error(f"Failed to get memory from vector DB: {e}")
        
        # Check fallback storage
        return self.fallback_storage.get(memory_id)
    
    def update_memory(
        self, 
        memory_id: str, 
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update an existing memory"""
        if self.vector_db:
            try:
                # Get existing memory
                existing = self.vector_db.get(ids=[memory_id])
                if not existing['documents']:
                    return False
                
                # Update content and metadata
                new_content = content if content is not None else existing['documents'][0]
                new_metadata = existing['metadatas'][0].copy()
                if metadata:
                    new_metadata.update(metadata)
                new_metadata["updated_at"] = datetime.now().isoformat()
                
                # Delete old and add new (ChromaDB doesn't have direct update)
                self.vector_db.delete(ids=[memory_id])
                self.vector_db.add(
                    documents=[new_content],
                    metadatas=[new_metadata],
                    ids=[memory_id]
                )
                
                logger.debug(f"Updated memory: {memory_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to update memory: {e}")
                return False
        else:
            # Update fallback storage
            if memory_id in self.fallback_storage:
                if content is not None:
                    self.fallback_storage[memory_id]["content"] = content
                if metadata:
                    self.fallback_storage[memory_id]["metadata"].update(metadata)
                return True
            return False
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory"""
        if self.vector_db:
            try:
                self.vector_db.delete(ids=[memory_id])
                logger.debug(f"Deleted memory: {memory_id}")
                return True
            except Exception as e:
                logger.error(f"Failed to delete memory: {e}")
                return False
        else:
            # Delete from fallback storage
            if memory_id in self.fallback_storage:
                del self.fallback_storage[memory_id]
                return True
            return False
    
    def get_similar_memories(
        self, 
        content: str, 
        limit: int = 3,
        exclude_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get memories similar to given content"""
        memories = self.search_memories(content, limit=limit + 1)
        
        # Filter out the excluded ID
        if exclude_id:
            memories = [m for m in memories if m["id"] != exclude_id]
        
        return memories[:limit]
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory storage statistics"""
        if self.vector_db:
            try:
                count = self.vector_db.count()
                return {
                    "total_memories": count,
                    "storage_type": "vector_db",
                    "collection_name": self.collection_name
                }
            except Exception as e:
                logger.error(f"Failed to get vector DB stats: {e}")
        
        return {
            "total_memories": len(self.fallback_storage),
            "storage_type": "fallback",
            "collection_name": self.collection_name
        }
    
    def _generate_memory_id(self, content: str) -> str:
        """Generate unique ID for memory"""
        # Use content hash + timestamp for uniqueness
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        timestamp = str(int(datetime.now().timestamp()))
        return f"mem_{content_hash}_{timestamp}"
    
    def _add_to_fallback(self, memory_id: str, content: str, metadata: Dict[str, Any]):
        """Add memory to fallback storage"""
        self.fallback_storage[memory_id] = {
            "id": memory_id,
            "content": content,
            "metadata": metadata
        }
        logger.debug(f"Added memory to fallback storage: {memory_id}")
    
    def _search_fallback(
        self, 
        query: str, 
        memory_type: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Search fallback storage using simple text matching"""
        results = []
        query_lower = query.lower()
        
        for memory in self.fallback_storage.values():
            # Filter by memory type
            if memory_type and memory["metadata"].get("memory_type") != memory_type:
                continue
            
            # Simple text similarity (contains check)
            content_lower = memory["content"].lower()
            if query_lower in content_lower:
                # Calculate simple similarity score
                similarity = len(query_lower) / len(content_lower)
                memory_copy = memory.copy()
                memory_copy["similarity"] = similarity
                results.append(memory_copy)
        
        # Sort by similarity and return top results
        results.sort(key=lambda x: x["similarity"], reverse=True)
        return results[:limit]
    
    def clear_memories(self, memory_type: Optional[str] = None):
        """Clear memories, optionally filtered by type"""
        if self.vector_db:
            try:
                if memory_type:
                    # Get IDs of memories to delete
                    results = self.vector_db.get(where={"memory_type": memory_type})
                    if results['ids']:
                        self.vector_db.delete(ids=results['ids'])
                        logger.info(f"Cleared {len(results['ids'])} memories of type {memory_type}")
                else:
                    # Clear all memories
                    self.vector_db.delete()
                    logger.info("Cleared all memories from vector DB")
            except Exception as e:
                logger.error(f"Failed to clear memories: {e}")
        
        # Clear fallback storage
        if memory_type:
            to_delete = [
                mid for mid, mem in self.fallback_storage.items()
                if mem["metadata"].get("memory_type") == memory_type
            ]
            for mid in to_delete:
                del self.fallback_storage[mid]
        else:
            self.fallback_storage.clear()
            logger.info("Cleared fallback storage")
